//! Authentication handling for HTTP requests.
//! 
//! This module provides comprehensive authentication support including
//! Basic, Bearer token, NEGOTIATE, and NTLM authentication methods.

use crate::core::{AuthConfig, AuthMethod, Auth<PERSON>oken, Wli<PERSON><PERSON>r, WliR<PERSON>ult};
use base64::Engine;
use chrono::{DateTime, Utc};
use reqwest::RequestBuilder;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, instrument};

/// Authentication handler for HTTP requests
#[derive(Debug, Clone)]
pub struct AuthHandler {
    config: AuthConfig,
    token_cache: Arc<RwLock<Option<CachedToken>>>,
}

/// Cached authentication token with expiration
#[derive(Debug, Clone)]
struct CachedToken {
    token: AuthToken,
    cached_at: DateTime<Utc>,
}

impl AuthHandler {
    /// Create a new authentication handler
    pub fn new(config: AuthConfig) -> Self {
        Self {
            config,
            token_cache: Arc::new(RwLock::new(None)),
        }
    }

    /// Apply authentication to a request builder
    #[instrument(skip(self, request_builder))]
    pub async fn apply_auth(&self, request_builder: RequestBuilder) -> WliResult<RequestBuilder> {
        match self.config.method {
            AuthMethod::Basic => self.apply_basic_auth(request_builder).await,
            AuthMethod::Bearer => self.apply_bearer_auth(request_builder).await,
            AuthMethod::Negotiate => self.apply_negotiate_auth(request_builder).await,
            AuthMethod::Ntlm => self.apply_ntlm_auth(request_builder).await,
        }
    }

    /// Apply Basic authentication
    async fn apply_basic_auth(&self, request_builder: RequestBuilder) -> WliResult<RequestBuilder> {
        let username = self.config.username.as_ref().ok_or_else(|| {
            WliError::auth("Username required for Basic authentication")
        })?;
        
        let password = self.config.password.as_ref().ok_or_else(|| {
            WliError::auth("Password required for Basic authentication")
        })?;

        debug!("Applying Basic authentication for user: {}", username);
        Ok(request_builder.basic_auth(username, Some(password)))
    }

    /// Apply Bearer token authentication
    async fn apply_bearer_auth(&self, request_builder: RequestBuilder) -> WliResult<RequestBuilder> {
        let token = self.get_valid_token().await?;
        debug!("Applying Bearer token authentication");
        Ok(request_builder.bearer_auth(&token.token))
    }

    /// Apply NEGOTIATE authentication (SPNEGO/Kerberos)
    async fn apply_negotiate_auth(&self, request_builder: RequestBuilder) -> WliResult<RequestBuilder> {
        #[cfg(windows)]
        {
            // On Windows, use SSPI for NEGOTIATE authentication
            self.apply_windows_negotiate_auth(request_builder).await
        }
        #[cfg(not(windows))]
        {
            // On non-Windows systems, fall back to Basic auth or return error
            debug!("NEGOTIATE authentication not supported on this platform, falling back to Basic");
            self.apply_basic_auth(request_builder).await
        }
    }

    /// Apply NTLM authentication
    async fn apply_ntlm_auth(&self, request_builder: RequestBuilder) -> WliResult<RequestBuilder> {
        #[cfg(windows)]
        {
            // On Windows, use SSPI for NTLM authentication
            self.apply_windows_ntlm_auth(request_builder).await
        }
        #[cfg(not(windows))]
        {
            // On non-Windows systems, implement basic NTLM or fall back
            debug!("NTLM authentication not fully supported on this platform, falling back to Basic");
            self.apply_basic_auth(request_builder).await
        }
    }

    #[cfg(windows)]
    async fn apply_windows_negotiate_auth(&self, request_builder: RequestBuilder) -> WliResult<RequestBuilder> {
        // Implementation for Windows NEGOTIATE authentication using SSPI
        // This would require additional Windows-specific dependencies
        debug!("Windows NEGOTIATE authentication not yet implemented, falling back to Basic");
        self.apply_basic_auth(request_builder).await
    }

    #[cfg(windows)]
    async fn apply_windows_ntlm_auth(&self, request_builder: RequestBuilder) -> WliResult<RequestBuilder> {
        // Implementation for Windows NTLM authentication using SSPI
        // This would require additional Windows-specific dependencies
        debug!("Windows NTLM authentication not yet implemented, falling back to Basic");
        self.apply_basic_auth(request_builder).await
    }

    /// Get a valid authentication token, refreshing if necessary
    async fn get_valid_token(&self) -> WliResult<AuthToken> {
        let cache = self.token_cache.read().await;
        
        if let Some(cached) = cache.as_ref() {
            let now = Utc::now();
            let cache_duration = chrono::Duration::seconds(self.config.token_cache_duration as i64);
            
            // Check if token is still valid and not expired
            if cached.cached_at + cache_duration > now && cached.token.expires_at > now {
                debug!("Using cached authentication token");
                return Ok(cached.token.clone());
            }
        }
        
        drop(cache);
        
        // Token is expired or doesn't exist, need to refresh
        debug!("Authentication token expired or missing, refreshing");
        Err(WliError::auth("No valid authentication token available"))
    }

    /// Set a new authentication token
    pub async fn set_token(&mut self, token: String) -> WliResult<()> {
        let auth_token = AuthToken {
            token,
            expires_at: Utc::now() + chrono::Duration::hours(1), // Default 1 hour expiration
            token_type: "Bearer".to_string(),
        };

        let cached_token = CachedToken {
            token: auth_token,
            cached_at: Utc::now(),
        };

        let mut cache = self.token_cache.write().await;
        *cache = Some(cached_token);
        
        debug!("Authentication token updated");
        Ok(())
    }

    /// Clear the cached authentication token
    pub async fn clear_token(&mut self) -> WliResult<()> {
        let mut cache = self.token_cache.write().await;
        *cache = None;
        debug!("Authentication token cleared");
        Ok(())
    }

    /// Check if we have a valid authentication token
    pub async fn is_authenticated(&self) -> bool {
        self.get_valid_token().await.is_ok()
    }

    /// Create authorization header value for Basic auth
    fn create_basic_auth_header(username: &str, password: &str) -> String {
        let credentials = format!("{}:{}", username, password);
        let encoded = base64::engine::general_purpose::STANDARD.encode(credentials.as_bytes());
        format!("Basic {}", encoded)
    }

    /// Get the authentication method
    pub fn auth_method(&self) -> &AuthMethod {
        &self.config.method
    }

    /// Get the username if available
    pub fn username(&self) -> Option<&str> {
        self.config.username.as_deref()
    }
}

/// Authentication challenge parser for handling WWW-Authenticate headers
#[derive(Debug)]
pub struct AuthChallenge {
    pub scheme: String,
    pub realm: Option<String>,
    pub parameters: std::collections::HashMap<String, String>,
}

impl AuthChallenge {
    /// Parse WWW-Authenticate header value
    pub fn parse(header_value: &str) -> WliResult<Self> {
        let parts: Vec<&str> = header_value.splitn(2, ' ').collect();
        if parts.is_empty() {
            return Err(WliError::auth("Invalid WWW-Authenticate header"));
        }

        let scheme = parts[0].to_string();
        let mut realm = None;
        let mut parameters = std::collections::HashMap::new();

        if parts.len() > 1 {
            // Parse parameters
            for param in parts[1].split(',') {
                let param = param.trim();
                if let Some(eq_pos) = param.find('=') {
                    let key = param[..eq_pos].trim().to_string();
                    let value = param[eq_pos + 1..].trim().trim_matches('"').to_string();
                    
                    if key.to_lowercase() == "realm" {
                        realm = Some(value.clone());
                    }
                    parameters.insert(key, value);
                }
            }
        }

        Ok(Self {
            scheme,
            realm,
            parameters,
        })
    }

    /// Check if this challenge supports a specific authentication method
    pub fn supports_method(&self, method: &AuthMethod) -> bool {
        match method {
            AuthMethod::Basic => self.scheme.to_lowercase() == "basic",
            AuthMethod::Bearer => self.scheme.to_lowercase() == "bearer",
            AuthMethod::Negotiate => self.scheme.to_lowercase() == "negotiate",
            AuthMethod::Ntlm => self.scheme.to_lowercase() == "ntlm",
        }
    }
}
