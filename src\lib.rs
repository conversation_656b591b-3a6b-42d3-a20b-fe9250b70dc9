pub mod api;
pub mod utils;
pub mod validation;
use anyhow::{anyhow, Result};
use clap::{ArgGroup, Parser};
use std::{path::{Path, PathBuf}, str::FromStr};
use std::time::Duration;

#[derive(Parser, Debug)]
#[command(author, version, about = "A CLI tool for managing files and interacting with a WebViewer server.", long_about = None, disable_help_flag = true)]
#[command(group(
    ArgGroup::new("auth")
        .args(["host", "usr", "pwd"])
        .multiple(true)
        .requires_all(["host", "usr", "pwd"])
))]
// #[command(group(
//     ArgGroup::new("action")
//         .args(["files", "dir", "name", "health"])
//         .required(true)
//         .multiple(true)
// ))]
pub struct Args {
    /// The hostname or IP address of the WebViewer server.
    #[arg(short = 'h', long, required_unless_present_any = ["extensions", "help"])]
    pub host: Option<String>,

    /// The username for authentication.
    #[arg(short = 'U', long, required_unless_present_any = ["extensions", "help"])]
    pub usr: Option<String>,

    /// The password for authentication.
    #[arg(short = 'P', long, required_unless_present_any = ["extensions", "help"])]
    pub pwd: Option<String>,

    /// Paths to the files to be uploaded, separated by spaces.
    #[arg(short = 'f', long, num_args(0..), conflicts_with_all = ["dir", "health"], required_unless_present_any = ["extensions", "help", "dir", "health", "name"])]
    pub files: Option<Vec<CustomPath>>,

    /// Path to the directory containing files to be uploaded.
    #[arg(short = 'd', long, conflicts_with_all = ["files", "health"], required_unless_present_any = ["extensions", "help", "files", "health", "name"])]
    pub dir: Option<CustomPath>,

    /// The target folder's path or ID on the server.
    #[arg(short = 't', long, default_value = "root")]
    pub target: String,

    /// Use the folder ID instead of the path for the target folder.
    #[arg(short = 'i', long)]
    pub use_id: bool,

    /// Optional name of the new folder to create, with support for nested folders.
    #[arg(short = 'n', long, conflicts_with = "health")]
    pub name: Option<String>,

    /// Specify the default view permission for new folders (1: Manage, 2: Edit, 3: View).
    #[arg(long, value_parser = clap::value_parser!(i8).range(1..=3), default_value_t = 3)]
    pub permission_level: i8,

    /// Disable SSL certificate validation.
    #[arg(short = 'k', long)]
    pub insecure: bool,

    /// Specify the timeout duration for the request.
    #[arg(long, default_value = "30s", value_parser = parse_duration_string)]
    pub timeout: u64,

    /// Enable verbose output for debugging purposes.
    #[arg(short = 'v', long)]
    pub verbose: bool,

    /// List all available file extensions and exit.
    #[arg(long)]
    pub extensions: bool,

    /// Check the server health and authentication status, then exit.
    #[arg(long, conflicts_with_all = ["files", "dir"], required_unless_present_any = ["extensions", "help", "files", "dir", "name"])]
    pub health: bool,

    /// Show this help message and exit.
    #[arg(long)]
    pub help: bool,
}

// pub fn check_auth_args(args: &Args) -> anyhow::Result<()> {
//     if args.host.is_none() || args.usr.is_none() || args.pwd.is_none() {
//         return Err(anyhow::anyhow!("Host (-h), Username (-U), and Password (-P) are required for this operation"));
//     }
//     Ok(())
// }

#[derive(Clone, Debug)]
pub struct CustomPath(PathBuf);

impl FromStr for CustomPath {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        let path = PathBuf::from(s);
        if !path.exists() {
            return Err(anyhow!("Path does not exist"));
        }
        Ok(CustomPath(path))
    }
}

impl CustomPath {
    pub fn as_path(&self) -> &Path {
        self.0.as_path()
    }
}

pub fn parse_duration_string(s: &str) -> Result<u64, String> {
    let num_part = s.trim_end_matches(|c: char| !c.is_numeric());
    let suffix = &s[num_part.len()..];
    let num: u64 = num_part.parse().map_err(|_| "invalid number".to_string())?;
    let duration = match suffix {
        "h" => Duration::from_secs(num * 3600),
        "m" => Duration::from_secs(num * 60),
        "s" => Duration::from_secs(num),
        "ms" => Duration::from_millis(num),
        _ => return Err(format!("invalid suffix {suffix}")),
    };
    Ok(duration.as_secs())
}