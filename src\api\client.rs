//! Modern API client that consolidates all server communication.

use crate::core::{AppConfig, WliResult};
use crate::http::WliHttpClient;
use crate::services::{AuthService, UploadService, HealthService};
use crate::utils::remove_port_if_localhost;
use std::sync::Arc;
use tracing::{info, instrument};

/// Consolidated API client for all server operations
#[derive(Debug, Clone)]
pub struct ApiClient {
    pub auth_service: Arc<AuthService>,
    pub upload_service: Arc<UploadService>,
    pub health_service: Arc<HealthService>,
    base_url: String,
}

impl ApiClient {
    /// Create a new API client with the given configuration
    #[instrument(skip(config))]
    pub fn new(config: AppConfig) -> WliResult<Self> {
        let host = config.server.host.as_ref()
            .ok_or_else(|| crate::core::WliError::config("Server host is required"))?;
        let base_url = remove_port_if_localhost(host);
        
        // Create HTTP client
        let http_client = WliHttpClient::new(config.clone())?;
        
        // Create authentication service
        let auth_service = Arc::new(AuthService::new(
            http_client.clone(),
            config.clone(),
            base_url.clone(),
        ));
        
        // Create upload service
        let upload_service = Arc::new(UploadService::new(
            http_client.clone(),
            auth_service.clone(),
            base_url.clone(),
        ));
        
        // Create health service with authentication
        let health_service = Arc::new(HealthService::with_auth(
            http_client,
            auth_service.clone(),
            base_url.clone(),
        ));

        info!("API client initialized for: {}", base_url);

        Ok(Self {
            auth_service,
            upload_service,
            health_service,
            base_url,
        })
    }

    /// Create a new API client from individual components (for testing)
    pub fn from_components(
        auth_service: Arc<AuthService>,
        upload_service: Arc<UploadService>,
        health_service: Arc<HealthService>,
        base_url: String,
    ) -> Self {
        Self {
            auth_service,
            upload_service,
            health_service,
            base_url,
        }
    }

    /// Authenticate with the server
    #[instrument(skip(self))]
    pub async fn authenticate(&self) -> WliResult<()> {
        self.auth_service.authenticate().await?;
        info!("Authentication successful");
        Ok(())
    }

    /// Logout from the server
    #[instrument(skip(self))]
    pub async fn logout(&self) -> WliResult<()> {
        self.auth_service.logout().await?;
        info!("Logout successful");
        Ok(())
    }

    /// Upload files to the server
    #[instrument(skip(self, file_paths), fields(count = file_paths.len()))]
    pub async fn upload_files(
        &self,
        file_paths: &[String],
        target: &str,
        use_id: bool,
        permission_level: i8,
    ) -> WliResult<serde_json::Value> {
        self.upload_service
            .upload_files(file_paths, target, use_id, permission_level)
            .await
    }

    /// Upload files from a directory
    #[instrument(skip(self), fields(dir_path = %dir_path))]
    pub async fn upload_directory(
        &self,
        dir_path: &str,
        target: &str,
        use_id: bool,
        permission_level: i8,
    ) -> WliResult<serde_json::Value> {
        self.upload_service
            .upload_directory(dir_path, target, use_id, permission_level)
            .await
    }

    /// Create a folder on the server
    #[instrument(skip(self), fields(folder_name = %folder_name))]
    pub async fn create_folder(
        &self,
        folder_name: &str,
        parent_target: &str,
        use_id: bool,
        permission_level: i8,
    ) -> WliResult<String> {
        self.upload_service
            .create_folder(folder_name, parent_target, use_id, permission_level)
            .await
    }

    /// Perform a comprehensive health check
    #[instrument(skip(self))]
    pub async fn health_check(&self) -> WliResult<crate::services::HealthStatus> {
        self.health_service.comprehensive_health_check().await
    }

    /// Check server health only (no authentication required)
    #[instrument(skip(self))]
    pub async fn check_server_health(&self) -> WliResult<(Option<serde_json::Value>, bool)> {
        self.health_service.check_server_health().await
    }

    /// Check authentication status
    #[instrument(skip(self))]
    pub async fn check_authentication(&self) -> WliResult<bool> {
        self.health_service.check_authentication().await
    }

    /// Get the current authenticated user
    pub async fn current_user(&self) -> Option<crate::core::User> {
        self.auth_service.current_user().await
    }

    /// Check if currently authenticated
    pub async fn is_authenticated(&self) -> bool {
        self.auth_service.is_authenticated().await
    }

    /// Get the base URL
    pub fn base_url(&self) -> &str {
        &self.base_url
    }

    /// Refresh authentication token if needed
    #[instrument(skip(self))]
    pub async fn refresh_token_if_needed(&self) -> WliResult<()> {
        self.auth_service.refresh_token_if_needed().await
    }

    /// Perform a complete upload workflow with folder creation
    #[instrument(skip(self, file_paths), fields(count = file_paths.len()))]
    pub async fn upload_with_folder_creation(
        &self,
        file_paths: &[String],
        target: &str,
        use_id: bool,
        folder_name: Option<&str>,
        permission_level: i8,
    ) -> WliResult<serde_json::Value> {
        // Ensure authentication
        self.refresh_token_if_needed().await?;

        let final_target = if let Some(folder_name) = folder_name {
            // Create folder first
            let folder_id = self
                .create_folder(folder_name, target, use_id, permission_level)
                .await?;
            
            info!("Created folder '{}' with ID: {}", folder_name, folder_id);
            info!("File manager URL: {}/file-manager/{}", self.base_url, folder_id);
            
            folder_id
        } else {
            target.to_string()
        };

        // Upload files to the target (original target or new folder)
        self.upload_files(file_paths, &final_target, true, permission_level)
            .await
    }

    /// Perform a complete directory upload workflow with folder creation
    #[instrument(skip(self), fields(dir_path = %dir_path))]
    pub async fn upload_directory_with_folder_creation(
        &self,
        dir_path: &str,
        target: &str,
        use_id: bool,
        folder_name: Option<&str>,
        permission_level: i8,
    ) -> WliResult<serde_json::Value> {
        // Ensure authentication
        self.refresh_token_if_needed().await?;

        let final_target = if let Some(folder_name) = folder_name {
            // Create folder first
            let folder_id = self
                .create_folder(folder_name, target, use_id, permission_level)
                .await?;
            
            info!("Created folder '{}' with ID: {}", folder_name, folder_id);
            info!("File manager URL: {}/file-manager/{}", self.base_url, folder_id);
            
            folder_id
        } else {
            target.to_string()
        };

        // Upload directory to the target (original target or new folder)
        self.upload_directory(dir_path, &final_target, true, permission_level)
            .await
    }
}
