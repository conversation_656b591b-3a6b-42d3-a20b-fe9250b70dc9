//! Comprehensive error handling for the WLI CLI tool.
//! 
//! This module provides structured error types that cover all possible
//! failure scenarios in the application with detailed context.

use thiserror::Error;

/// Main error type for the WLI application
#[derive(Error, Debug)]
pub enum WliError {
    /// Configuration-related errors
    #[error("Configuration error: {message}")]
    Config { message: String },

    /// Authentication and authorization errors
    #[error("Authentication failed: {message}")]
    Auth { message: String },

    /// Network and HTTP-related errors
    #[error("Network error: {message}")]
    Network { message: String },

    /// File system and I/O errors
    #[error("File system error: {message}")]
    FileSystem { message: String },

    /// File validation errors
    #[error("File validation error: {message}")]
    FileValidation { message: String },

    /// Upload-specific errors
    #[error("Upload error: {message}")]
    Upload { message: String },

    /// Proxy authentication errors
    #[error("Proxy authentication error: {message}")]
    ProxyAuth { message: String },

    /// Server response errors
    #[error("Server error (code: {code}): {message}")]
    Server { code: u16, message: String },

    /// API-related errors
    #[error("API error: {message}")]
    Api { message: String },

    /// CLI argument parsing errors
    #[error("Invalid argument: {message}")]
    InvalidArgument { message: String },

    /// Generic application errors
    #[error("Application error: {message}")]
    Application { message: String },
}

impl WliError {
    /// Create a new configuration error
    pub fn config<S: Into<String>>(message: S) -> Self {
        Self::Config {
            message: message.into(),
        }
    }

    /// Create a new authentication error
    pub fn auth<S: Into<String>>(message: S) -> Self {
        Self::Auth {
            message: message.into(),
        }
    }

    /// Create a new network error
    pub fn network<S: Into<String>>(message: S) -> Self {
        Self::Network {
            message: message.into(),
        }
    }

    /// Create a new file system error
    pub fn file_system<S: Into<String>>(message: S) -> Self {
        Self::FileSystem {
            message: message.into(),
        }
    }

    /// Create a new file validation error
    pub fn file_validation<S: Into<String>>(message: S) -> Self {
        Self::FileValidation {
            message: message.into(),
        }
    }

    /// Create a new upload error
    pub fn upload<S: Into<String>>(message: S) -> Self {
        Self::Upload {
            message: message.into(),
        }
    }

    /// Create a new proxy authentication error
    pub fn proxy_auth<S: Into<String>>(message: S) -> Self {
        Self::ProxyAuth {
            message: message.into(),
        }
    }

    /// Create a new server error
    pub fn server<S: Into<String>>(code: u16, message: S) -> Self {
        Self::Server {
            code,
            message: message.into(),
        }
    }

    /// Create a new invalid argument error
    pub fn invalid_argument<S: Into<String>>(message: S) -> Self {
        Self::InvalidArgument {
            message: message.into(),
        }
    }

    /// Create a new application error
    pub fn application<S: Into<String>>(message: S) -> Self {
        Self::Application {
            message: message.into(),
        }
    }

    /// Create a new API error
    pub fn api<S: Into<String>>(message: S) -> Self {
        Self::Api {
            message: message.into(),
        }
    }
}

/// Result type alias for WLI operations
pub type WliResult<T> = Result<T, WliError>;

/// Convert from anyhow::Error to WliError
impl From<anyhow::Error> for WliError {
    fn from(err: anyhow::Error) -> Self {
        WliError::Application {
            message: err.to_string(),
        }
    }
}

/// Convert from reqwest::Error to WliError
impl From<reqwest::Error> for WliError {
    fn from(err: reqwest::Error) -> Self {
        if err.is_timeout() {
            WliError::Network {
                message: "Request timeout".to_string(),
            }
        } else if err.is_connect() {
            WliError::Network {
                message: format!("Connection failed: {}", err),
            }
        } else {
            WliError::Network {
                message: err.to_string(),
            }
        }
    }
}

/// Convert from std::io::Error to WliError
impl From<std::io::Error> for WliError {
    fn from(err: std::io::Error) -> Self {
        WliError::FileSystem {
            message: err.to_string(),
        }
    }
}

/// Convert from serde_json::Error to WliError
impl From<serde_json::Error> for WliError {
    fn from(err: serde_json::Error) -> Self {
        WliError::Application {
            message: format!("JSON parsing error: {}", err),
        }
    }
}

/// Convert from config::ConfigError to WliError
impl From<config::ConfigError> for WliError {
    fn from(err: config::ConfigError) -> Self {
        WliError::Config {
            message: format!("Configuration error: {}", err),
        }
    }
}
