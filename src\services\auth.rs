//! Authentication service for managing user sessions and tokens.

use crate::core::{AppConfig, AuthToken, User, Wli<PERSON>rror, WliResult};
use crate::http::WliHttpClient;
use serde_json::{json, Value};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, instrument, warn};

/// Authentication service for managing user sessions
#[derive(Debug, Clone)]
pub struct AuthService {
    client: Arc<RwLock<WliHttpClient>>,
    config: AppConfig,
    current_user: Arc<RwLock<Option<User>>>,
    base_url: String,
}

impl AuthService {
    /// Create a new authentication service
    pub fn new(client: WliHttpClient, config: AppConfig, base_url: String) -> Self {
        Self {
            client: Arc::new(RwLock::new(client)),
            config,
            current_user: Arc::new(RwLock::new(None)),
            base_url: base_url.trim_end_matches('/').to_string(),
        }
    }

    /// Authenticate with the server using configured credentials
    #[instrument(skip(self), fields(base_url = %self.base_url))]
    pub async fn authenticate(&self) -> WliResult<AuthToken> {
        let username = self.config.auth.username.as_ref().ok_or_else(|| {
            WliError::auth("Username is required for authentication")
        })?;

        let password = self.config.auth.password.as_ref().ok_or_else(|| {
            WliError::auth("Password is required for authentication")
        })?;

        info!("Authenticating user: {}", username);

        let auth_url = format!("{}/api/auth/login", self.base_url);
        let auth_data = json!({
            "username": username,
            "password": password
        });

        let client = self.client.read().await;
        let response = client.post_form(&auth_url, &[
            ("username", username.as_str()),
            ("password", password.as_str()),
        ]).await?;

        let response_data: Value = response.json().await.map_err(|e| {
            WliError::auth(format!("Failed to parse authentication response: {}", e))
        })?;

        // Check if authentication was successful
        let code = response_data.get("code")
            .and_then(|c| c.as_i64())
            .ok_or_else(|| WliError::auth("Invalid response format"))?;

        if code != 200 {
            let message = response_data.get("message")
                .and_then(|m| m.as_str())
                .unwrap_or("Authentication failed");
            return Err(WliError::auth(message));
        }

        // Extract token and user information
        let data = response_data.get("data")
            .ok_or_else(|| WliError::auth("Missing data in response"))?;

        let token_str = data.get("token")
            .and_then(|t| t.as_str())
            .ok_or_else(|| WliError::auth("Missing token in response"))?;

        let user_data = data.get("user")
            .ok_or_else(|| WliError::auth("Missing user data in response"))?;

        let user: User = serde_json::from_value(user_data.clone()).map_err(|e| {
            WliError::auth(format!("Failed to parse user data: {}", e))
        })?;

        // Create auth token
        let auth_token = AuthToken {
            token: token_str.to_string(),
            expires_at: chrono::Utc::now() + chrono::Duration::hours(1), // Default 1 hour
            token_type: "Bearer".to_string(),
        };

        // Update client with new token
        let mut client_mut = self.client.write().await;
        client_mut.update_auth_token(token_str.to_string()).await?;
        drop(client_mut);

        // Store current user
        let mut current_user = self.current_user.write().await;
        *current_user = Some(user.clone());
        drop(current_user);

        info!("Authentication successful for user: {} (ID: {})", user.username, user.id);
        debug!("Auth token expires at: {}", auth_token.expires_at);

        Ok(auth_token)
    }

    /// Logout and clear authentication state
    #[instrument(skip(self))]
    pub async fn logout(&self) -> WliResult<()> {
        info!("Logging out user");

        let logout_url = format!("{}/api/auth/logout", self.base_url);
        
        // Attempt to logout on server (best effort)
        let client = self.client.read().await;
        if let Err(e) = client.post_json(&logout_url, &json!({})).await {
            warn!("Failed to logout on server: {}", e);
            // Continue with local cleanup even if server logout fails
        }
        drop(client);

        // Clear authentication state
        let mut client_mut = self.client.write().await;
        client_mut.clear_auth_token().await?;
        drop(client_mut);

        let mut current_user = self.current_user.write().await;
        *current_user = None;

        info!("Logout completed");
        Ok(())
    }

    /// Check if currently authenticated
    pub async fn is_authenticated(&self) -> bool {
        let client = self.client.read().await;
        client.is_authenticated().await
    }

    /// Get the current authenticated user
    pub async fn current_user(&self) -> Option<User> {
        let current_user = self.current_user.read().await;
        current_user.clone()
    }

    /// Refresh the authentication token if needed
    #[instrument(skip(self))]
    pub async fn refresh_token_if_needed(&self) -> WliResult<()> {
        if !self.is_authenticated().await {
            debug!("Token refresh needed, re-authenticating");
            self.authenticate().await?;
        }
        Ok(())
    }

    /// Validate current session with the server
    #[instrument(skip(self))]
    pub async fn validate_session(&self) -> WliResult<bool> {
        let validate_url = format!("{}/api/auth/validate", self.base_url);
        
        let client = self.client.read().await;
        match client.get(&validate_url).await {
            Ok(response) => {
                let response_data: Value = response.json().await.map_err(|e| {
                    WliError::auth(format!("Failed to parse validation response: {}", e))
                })?;

                let is_valid = response_data.get("valid")
                    .and_then(|v| v.as_bool())
                    .unwrap_or(false);

                debug!("Session validation result: {}", is_valid);
                Ok(is_valid)
            }
            Err(e) => {
                warn!("Session validation failed: {}", e);
                Ok(false)
            }
        }
    }

    /// Get user profile information
    #[instrument(skip(self))]
    pub async fn get_user_profile(&self) -> WliResult<User> {
        let profile_url = format!("{}/api/user/profile", self.base_url);
        
        let client = self.client.read().await;
        let response = client.get(&profile_url).await?;

        let response_data: Value = response.json().await.map_err(|e| {
            WliError::auth(format!("Failed to parse profile response: {}", e))
        })?;

        let code = response_data.get("code")
            .and_then(|c| c.as_i64())
            .ok_or_else(|| WliError::auth("Invalid response format"))?;

        if code != 200 {
            let message = response_data.get("message")
                .and_then(|m| m.as_str())
                .unwrap_or("Failed to get user profile");
            return Err(WliError::auth(message));
        }

        let user_data = response_data.get("data")
            .ok_or_else(|| WliError::auth("Missing user data in response"))?;

        let user: User = serde_json::from_value(user_data.clone()).map_err(|e| {
            WliError::auth(format!("Failed to parse user data: {}", e))
        })?;

        // Update current user cache
        let mut current_user = self.current_user.write().await;
        *current_user = Some(user.clone());

        debug!("Retrieved user profile: {} ({})", user.username, user.id);
        Ok(user)
    }

    /// Get the HTTP client (for use by other services)
    pub async fn client(&self) -> Arc<RwLock<WliHttpClient>> {
        self.client.clone()
    }

    /// Get the base URL
    pub fn base_url(&self) -> &str {
        &self.base_url
    }

    /// Get the configuration
    pub fn config(&self) -> &AppConfig {
        &self.config
    }
}
