use super::{endpoint::RequestData, Endpoint};
use crate::Args;
use anyhow::Result;
use log::{debug, error, info};
use reqwest::multipart::{Form, Part};
use serde_json::{json, Value};
use std::path::Path;
use tokio::fs::File;
use tokio_util::codec::{BytesCodec, FramedRead};
use super::request::*;

#[derive(Debu<PERSON>, Clone)]
pub struct UploadAPI {
    pub endpoint: Endpoint,
    file_paths: Option<Vec<String>>,
    upload_target: String,
    target_type: String,
    folder_name: String,
    dir: String,
    permission_level: i8,
}

impl UploadAPI {
    pub fn new(args: &Args) -> Self {
        let host = args
            .host
            .as_ref()
            .expect("Host is required for health check");
        let usr = args
            .usr
            .as_ref()
            .expect("Username is required for health check");
        let pwd = args
            .pwd
            .as_ref()
            .expect("Password is required for health check");

        Self {
            endpoint: Endpoint::new(host, usr, pwd, args.insecure, args.timeout),

            file_paths: args.files.as_ref().map(|f| {
                f.iter()
                    .map(|p| p.as_path().to_string_lossy().into_owned())
                    .collect()
            }),
            upload_target: args.target.clone(),
            target_type: if args.use_id {
                "uploadId"
            } else {
                "uploadPath"
            }
            .to_string(),
            folder_name: args.name.clone().unwrap_or(String::new()),
            dir: args.dir.as_ref().map_or("".to_string(), |d| {
                d.as_path().to_string_lossy().into_owned()
            }),
            permission_level: args.permission_level,
        }
    }

    pub async fn run(&mut self) -> Result<()> {
        self.endpoint.do_auth().await?;
        self.upload_master().await?;
        self.endpoint.do_logout().await?;
        Ok(())
    }

    async fn upload_master(&mut self) -> Result<()> {
        if self.folder_name.is_empty() {
            self.do_upload().await?;
        } else {
            let target_id = self.create_folders().await?;
            info!("{}/file-manager/{}", self.endpoint.host, target_id);
            self.do_upload().await?;
        }
        Ok(())
    }

    async fn set_default_teams(&self, folder_id: String) -> Result<()> {
        let params = FolderUpdateRequest {
            id: folder_id,
            team_members: vec![MemberInfo {
                id: "default_team_id".to_string(),
                permission: self.permission_level,
            }],
        };

        let result = self
            .endpoint
            .put(
                "/api/folders",
                Some(RequestData::Json(json!(params))),
            )
            .await?;

        if result["code"] == 200 {
            Ok(())
        } else {
            error!("Failed to update folder");
            anyhow::bail!("Failed to update folder");
        }
    }

    async fn create_folders(&mut self) -> Result<String> {
        let name_array: Vec<&str> = self.folder_name.split('/').collect();
        for folder_name in name_array {
            debug!("Creating \"{}\"...", folder_name);
            let params = vec![
                (self.target_type.to_string(), self.upload_target.to_string()),
                ("name".to_owned(), folder_name.to_string()),
            ];

            let result = self
                .endpoint
                .post("/api/folders", Some(RequestData::Form(params)))
                .await?;

            if result["code"] == 201 {
                self.target_type = String::from("uploadId");
                let folder_id = result["data"]["id"].as_str().unwrap().to_string();
                self.upload_target = folder_id.clone();
                let _ = self.set_default_teams(folder_id).await;
            } else {
                if result["message"] == "Name already exists on destination folder." {
                    debug!("Skipped...");
                    self.target_type = String::from("uploadPath");
                    self.upload_target = format!("{}/{}", self.upload_target, folder_name);
                    continue;
                }
                error!("Unable to create folder \"{}\"", folder_name);
                anyhow::bail!("Failed to create folder. {:?}", result["message"]);
            }
        }

        if self.target_type != "uploadPath" {
            Ok(self.upload_target.clone())
        } else {
            anyhow::bail!("Cannot create folder with given name!");
        }
    }

    async fn do_upload(&self) -> anyhow::Result<()> {
        let file_paths: Vec<String> = if self.file_paths.is_none() && !self.dir.is_empty() {
            crate::utils::get_files_in_dir(&self.dir).expect("Failed to get files from directory")
        } else {
            self.file_paths.clone().unwrap_or_default()
        };

        match file_paths.len() {
            1 => self.upload_single_file(&file_paths[0]).await,
            len if len > 1 => self.upload_bulk_files(&file_paths).await,
            _ => Ok(()), // No files to upload
        }
    }

    async fn upload_single_file(&self, file_path: &str) -> anyhow::Result<()> {
        let result = self.single_upload(file_path).await?;
        self.handle_upload_result(&result, false).await
    }

    async fn upload_bulk_files(&self, file_paths: &[String]) -> anyhow::Result<()> {
        let result = self.bulk_upload(file_paths).await?;
        self.handle_upload_result(&result, true).await
    }

    async fn handle_upload_result(&self, result: &Value, is_bulk: bool) -> anyhow::Result<()> {
        let code = result["code"]
            .as_i64()
            .ok_or_else(|| anyhow::anyhow!("Missing or invalid 'code' in response"))?;

        if code == 201 {
            if is_bulk {
                for file_info in result["data"]
                    .as_array()
                    .ok_or_else(|| anyhow::anyhow!("Invalid 'data' format"))?
                {
                    if let Some(id) = file_info["id"].as_str() {
                        info!("{}/view/{}", self.endpoint.host, id);
                    }
                }
            } else {
                if let Some(id) = result["data"]["id"].as_str() {
                    info!("{}/view/{}", self.endpoint.host, id);
                }
            }
        } else {
            let message = result["message"].as_str().unwrap_or("Unknown error");
            anyhow::bail!("{}", message);
        }

        Ok(())
    }

    async fn single_upload(&self, file_path: &str) -> Result<serde_json::Value> {
        let file = File::open(file_path).await?;
        let stream = FramedRead::new(file, BytesCodec::new());
        let file_name = Path::new(file_path)
            .file_name()
            .ok_or_else(|| anyhow::anyhow!("Invalid file path"))?
            .to_str()
            .ok_or_else(|| anyhow::anyhow!("Invalid file name"))?;

        let part = Part::stream(reqwest::Body::wrap_stream(stream))
            .file_name(file_name.to_string())
            .mime_str("application/zip")?;

        let form = Form::new()
            .text(self.target_type.clone(), self.upload_target.clone())
            .part("fileUpload", part);

        self.endpoint
            .post("/api/files", Some(RequestData::MultiPart(form)))
            .await
    }

    async fn bulk_upload(&self, file_paths: &[String]) -> Result<serde_json::Value> {
        let mut form = Form::new();
        form = form.text(self.target_type.clone(), self.upload_target.clone());

        for file_path in file_paths {
            let file = File::open(file_path).await?;
            let stream = FramedRead::new(file, BytesCodec::new());
            let file_name = Path::new(file_path).file_name().unwrap().to_str().unwrap();

            let part = Part::stream(reqwest::Body::wrap_stream(stream))
                .file_name(file_name.to_string())
                .mime_str("application/zip")?;

            form = form.part("fileUpload", part);
        }
        self.endpoint
            .post("/api/files/bulk", Some(RequestData::MultiPart(form)))
            .await
    }

    pub async fn do_logout(&mut self) -> Result<()> {
        self.endpoint.do_logout().await
    }
    pub async fn check_health(&mut self) -> Result<()> {
        let (health_result, is_live) = self.endpoint.check_health().await?;

        if let Some(health_result) = health_result {
            debug!("{}", serde_json::to_string_pretty(&health_result)?);
        }

        if is_live {
            info!("Server status: LIVE");
            let is_authenticated = &self.endpoint.check_auth(false).await?;
            info!(
                "Authentication status: {}",
                if is_authenticated.to_owned() {
                    "SUCCESS"
                } else {
                    "FAILED"
                }
            );
        } else {
            error!("Server status: NOT LIVE");
        }

        Ok(())
    }
}
