//! Progress tracking and display utilities.

use crate::utils::format::{format_percentage, format_speed};
use indicatif::{ProgressBar, ProgressStyle};
use std::time::{Duration, Instant};

/// Progress tracker for file uploads
#[derive(Debug)]
pub struct UploadProgressTracker {
    progress_bar: ProgressBar,
    start_time: Instant,
    total_bytes: u64,
    uploaded_bytes: u64,
}

impl UploadProgressTracker {
    /// Create a new progress tracker
    pub fn new(total_bytes: u64, file_name: &str) -> Self {
        let progress_bar = ProgressBar::new(total_bytes);
        progress_bar.set_style(
            ProgressStyle::default_bar()
                .template("{spinner:.green} [{elapsed_precise}] [{wide_bar:.cyan/blue}] {bytes}/{total_bytes} ({eta})")
                .unwrap()
                .progress_chars("#>-"),
        );
        progress_bar.set_message(format!("Uploading {}", file_name));

        Self {
            progress_bar,
            start_time: Instant::now(),
            total_bytes,
            uploaded_bytes: 0,
        }
    }

    /// Update progress with new uploaded bytes
    pub fn update(&mut self, uploaded_bytes: u64) {
        self.uploaded_bytes = uploaded_bytes;
        self.progress_bar.set_position(uploaded_bytes);
        
        // Update speed information
        let elapsed = self.start_time.elapsed();
        if elapsed.as_secs() > 0 {
            let speed = uploaded_bytes as f64 / elapsed.as_secs_f64();
            self.progress_bar.set_message(format!(
                "Uploading at {}",
                format_speed(speed)
            ));
        }
    }

    /// Mark upload as completed
    pub fn finish(&self) {
        self.progress_bar.finish_with_message("Upload completed");
    }

    /// Mark upload as failed
    pub fn finish_with_error(&self, error: &str) {
        self.progress_bar.finish_with_message(format!("Upload failed: {}", error));
    }

    /// Get current progress percentage
    pub fn progress_percentage(&self) -> f64 {
        if self.total_bytes == 0 {
            0.0
        } else {
            (self.uploaded_bytes as f64 / self.total_bytes as f64) * 100.0
        }
    }

    /// Get current upload speed in bytes per second
    pub fn current_speed(&self) -> f64 {
        let elapsed = self.start_time.elapsed();
        if elapsed.as_secs() > 0 {
            self.uploaded_bytes as f64 / elapsed.as_secs_f64()
        } else {
            0.0
        }
    }

    /// Get estimated time remaining
    pub fn eta(&self) -> Option<Duration> {
        let speed = self.current_speed();
        if speed > 0.0 && self.uploaded_bytes < self.total_bytes {
            let remaining_bytes = self.total_bytes - self.uploaded_bytes;
            let eta_seconds = remaining_bytes as f64 / speed;
            Some(Duration::from_secs_f64(eta_seconds))
        } else {
            None
        }
    }
}

/// Multi-file progress tracker
#[derive(Debug)]
pub struct MultiFileProgressTracker {
    overall_progress: ProgressBar,
    current_file_progress: Option<UploadProgressTracker>,
    total_files: usize,
    completed_files: usize,
    #[allow(dead_code)]
    total_bytes: u64,
    uploaded_bytes: u64,
    start_time: Instant,
}

impl MultiFileProgressTracker {
    /// Create a new multi-file progress tracker
    pub fn new(total_files: usize, total_bytes: u64) -> Self {
        let overall_progress = ProgressBar::new(total_files as u64);
        overall_progress.set_style(
            ProgressStyle::default_bar()
                .template("{spinner:.green} Overall: [{wide_bar:.cyan/blue}] {pos}/{len} files ({percent}%)")
                .unwrap()
                .progress_chars("#>-"),
        );

        Self {
            overall_progress,
            current_file_progress: None,
            total_files,
            completed_files: 0,
            total_bytes,
            uploaded_bytes: 0,
            start_time: Instant::now(),
        }
    }

    /// Start tracking a new file
    pub fn start_file(&mut self, file_size: u64, file_name: &str) {
        self.current_file_progress = Some(UploadProgressTracker::new(file_size, file_name));
    }

    /// Update current file progress
    pub fn update_file_progress(&mut self, uploaded_bytes: u64) {
        if let Some(ref mut tracker) = self.current_file_progress {
            tracker.update(uploaded_bytes);
        }
    }

    /// Mark current file as completed
    pub fn complete_file(&mut self) {
        if let Some(ref tracker) = self.current_file_progress {
            tracker.finish();
            self.uploaded_bytes += tracker.total_bytes;
        }
        
        self.completed_files += 1;
        self.overall_progress.set_position(self.completed_files as u64);
        self.current_file_progress = None;
    }

    /// Mark current file as failed
    pub fn fail_file(&mut self, error: &str) {
        if let Some(ref tracker) = self.current_file_progress {
            tracker.finish_with_error(error);
        }
        
        self.completed_files += 1;
        self.overall_progress.set_position(self.completed_files as u64);
        self.current_file_progress = None;
    }

    /// Finish all progress tracking
    pub fn finish(&self) {
        self.overall_progress.finish_with_message("All uploads completed");
    }

    /// Get overall progress percentage
    pub fn overall_progress_percentage(&self) -> f64 {
        if self.total_files == 0 {
            0.0
        } else {
            (self.completed_files as f64 / self.total_files as f64) * 100.0
        }
    }

    /// Get overall upload speed
    pub fn overall_speed(&self) -> f64 {
        let elapsed = self.start_time.elapsed();
        if elapsed.as_secs() > 0 {
            self.uploaded_bytes as f64 / elapsed.as_secs_f64()
        } else {
            0.0
        }
    }
}

/// Simple progress reporter for console output
#[derive(Debug)]
pub struct SimpleProgressReporter {
    total_items: usize,
    completed_items: usize,
    start_time: Instant,
}

impl SimpleProgressReporter {
    /// Create a new simple progress reporter
    pub fn new(total_items: usize) -> Self {
        Self {
            total_items,
            completed_items: 0,
            start_time: Instant::now(),
        }
    }

    /// Report progress for an item
    pub fn report_progress(&mut self, item_name: &str) {
        self.completed_items += 1;
        let percentage = if self.total_items > 0 {
            (self.completed_items as f64 / self.total_items as f64) * 100.0
        } else {
            0.0
        };

        println!(
            "[{}/{}] ({}) {}",
            self.completed_items,
            self.total_items,
            format_percentage(percentage),
            item_name
        );
    }

    /// Report completion
    pub fn finish(&self) {
        let elapsed = self.start_time.elapsed();
        println!(
            "Completed {} items in {}",
            self.completed_items,
            crate::utils::format::format_duration(elapsed)
        );
    }
}

/// Create a spinner for long-running operations
pub fn create_spinner(message: &str) -> ProgressBar {
    let spinner = ProgressBar::new_spinner();
    spinner.set_style(
        ProgressStyle::default_spinner()
            .template("{spinner:.green} {msg}")
            .unwrap(),
    );
    spinner.set_message(message.to_string());
    spinner.enable_steady_tick(Duration::from_millis(100));
    spinner
}
