//! System utilities and platform-specific functionality.

use std::env;
use std::path::PathBuf;
use std::time::Duration;

/// Get the current user's home directory
pub fn get_home_directory() -> Option<PathBuf> {
    dirs::home_dir()
}

/// Get the current user's config directory
pub fn get_config_directory() -> Option<PathBuf> {
    dirs::config_dir()
}

/// Get the current user's cache directory
pub fn get_cache_directory() -> Option<PathBuf> {
    dirs::cache_dir()
}

/// Get the application's config directory
pub fn get_app_config_directory() -> Option<PathBuf> {
    get_config_directory().map(|dir| dir.join("wli"))
}

/// Get the application's cache directory
pub fn get_app_cache_directory() -> Option<PathBuf> {
    get_cache_directory().map(|dir| dir.join("wli"))
}

/// Check if running on Windows
pub fn is_windows() -> bool {
    cfg!(target_os = "windows")
}

/// Check if running on macOS
pub fn is_macos() -> bool {
    cfg!(target_os = "macos")
}

/// Check if running on Linux
pub fn is_linux() -> bool {
    cfg!(target_os = "linux")
}

/// Get the current working directory
pub fn get_current_directory() -> std::io::Result<PathBuf> {
    env::current_dir()
}

/// Parse duration string (e.g., "30s", "5m", "1h")
pub fn parse_duration_string(s: &str) -> Result<Duration, String> {
    let s = s.trim();
    if s.is_empty() {
        return Err("Duration string cannot be empty".to_string());
    }

    let (number_part, suffix) = if let Some(pos) = s.find(|c: char| !c.is_ascii_digit()) {
        (&s[..pos], &s[pos..])
    } else {
        (s, "s") // Default to seconds if no suffix
    };

    let number: u64 = number_part.parse()
        .map_err(|_| format!("Invalid number: {}", number_part))?;

    let duration = match suffix.to_lowercase().as_str() {
        "ms" | "millis" | "milliseconds" => Duration::from_millis(number),
        "s" | "sec" | "secs" | "second" | "seconds" => Duration::from_secs(number),
        "m" | "min" | "mins" | "minute" | "minutes" => Duration::from_secs(number * 60),
        "h" | "hr" | "hrs" | "hour" | "hours" => Duration::from_secs(number * 3600),
        "d" | "day" | "days" => Duration::from_secs(number * 86400),
        _ => return Err(format!("Invalid duration suffix: {}", suffix)),
    };

    Ok(duration)
}

/// Get environment variable with default value
pub fn get_env_var_or_default(key: &str, default: &str) -> String {
    env::var(key).unwrap_or_else(|_| default.to_string())
}

/// Check if environment variable is set to a truthy value
pub fn is_env_var_truthy(key: &str) -> bool {
    match env::var(key) {
        Ok(value) => matches!(value.to_lowercase().as_str(), "1" | "true" | "yes" | "on"),
        Err(_) => false,
    }
}

/// Get the number of CPU cores
pub fn get_cpu_count() -> usize {
    num_cpus::get()
}

/// Get optimal number of concurrent operations based on CPU count
pub fn get_optimal_concurrency() -> usize {
    (get_cpu_count() * 2).min(8).max(2)
}

/// Check if stdout is a terminal (for colored output)
pub fn is_terminal() -> bool {
    atty::is(atty::Stream::Stdout)
}

/// Get terminal width for formatting
pub fn get_terminal_width() -> usize {
    if let Some((width, _)) = term_size::dimensions() {
        width
    } else {
        80 // Default width
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_duration_string() {
        assert_eq!(parse_duration_string("30s").unwrap(), Duration::from_secs(30));
        assert_eq!(parse_duration_string("5m").unwrap(), Duration::from_secs(300));
        assert_eq!(parse_duration_string("1h").unwrap(), Duration::from_secs(3600));
        assert_eq!(parse_duration_string("2d").unwrap(), Duration::from_secs(172800));
        assert_eq!(parse_duration_string("1000ms").unwrap(), Duration::from_millis(1000));
        assert_eq!(parse_duration_string("30").unwrap(), Duration::from_secs(30));
        
        assert!(parse_duration_string("invalid").is_err());
        assert!(parse_duration_string("30x").is_err());
        assert!(parse_duration_string("").is_err());
    }

    #[test]
    fn test_is_env_var_truthy() {
        env::set_var("TEST_TRUE_1", "1");
        env::set_var("TEST_TRUE_TRUE", "true");
        env::set_var("TEST_TRUE_YES", "yes");
        env::set_var("TEST_FALSE_0", "0");
        env::set_var("TEST_FALSE_FALSE", "false");
        
        assert!(is_env_var_truthy("TEST_TRUE_1"));
        assert!(is_env_var_truthy("TEST_TRUE_TRUE"));
        assert!(is_env_var_truthy("TEST_TRUE_YES"));
        assert!(!is_env_var_truthy("TEST_FALSE_0"));
        assert!(!is_env_var_truthy("TEST_FALSE_FALSE"));
        assert!(!is_env_var_truthy("NONEXISTENT_VAR"));
        
        // Clean up
        env::remove_var("TEST_TRUE_1");
        env::remove_var("TEST_TRUE_TRUE");
        env::remove_var("TEST_TRUE_YES");
        env::remove_var("TEST_FALSE_0");
        env::remove_var("TEST_FALSE_FALSE");
    }
}
