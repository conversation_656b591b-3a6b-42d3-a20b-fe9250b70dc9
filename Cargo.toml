[package]
name = "wli"
version = "1.5.4"
edition = "2021"
build = "build.rs"
authors = ["WebViewer Team"]
description = "A command-line interface (CLI) tool for uploading files to the WebViewer server"
license = "MIT"
repository = "https://github.com/your-org/wli"
keywords = ["cli", "upload", "webviewer", "files"]
categories = ["command-line-utilities"]

[dependencies]
# CLI and argument parsing
clap = { version = "4.5", features = ["derive", "env", "color"] }

# HTTP client with proxy support
reqwest = { version = "0.12", features = [
    "json",
    "multipart",
    "stream",
    "gzip",
    "brotli",
    "rustls-tls"
] }

# Async runtime
tokio = { version = "1.40", features = ["full"] }
tokio-util = { version = "0.7", features = ["codec", "io"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Logging
log = "0.4"
env_logger = "0.11"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Utilities
url = "2.5"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.10", features = ["v4", "serde"] }
mime_guess = "2.0"
indicatif = "0.17"
console = "0.15"
num_cpus = "1.16"
atty = "0.2"
term_size = "0.3"

# Configuration
config = "0.14"
dirs = "5.0"

# Security and authentication
base64 = "0.22"

# File handling
walkdir = "2.5"
globset = "0.4"

[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["winhttp", "sspi"] }

[target.'cfg(windows)'.build-dependencies]
winres = "0.1.12"

[dev-dependencies]
tempfile = "3.8"
mockito = "1.4"
tokio-test = "0.4"

[package.metadata.winres]
FileDescription = "A command-line interface (CLI) tool for uploading files to the WebViewer server"
LegalCopyright = "Copyright © 2024"
