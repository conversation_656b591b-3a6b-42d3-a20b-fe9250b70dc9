[package]
name = "wli"
version = "1.5.4"
edition = "2021"
build = "build.rs"

[dependencies]
clap = { version = "4.3", features = ["derive"] }
reqwest = { version = "0.11", features = ["json", "multipart", "stream"] }
tokio = { version = "1", features = ["full"] }
tokio-util = "0.7.11"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"
url = "2.3"
chrono = "0.4.38"

[target.'cfg(windows)'.build-dependencies]
winres = "0.1.12"

[package.metadata.winres]
FileDescription = "A command-line interface (CLI) tool for uploading files to the WebViewer server"
LegalCopyright = "Copyright © 2024"
