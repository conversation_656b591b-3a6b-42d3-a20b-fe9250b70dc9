# WebViewer CLI (wli)

A powerful command-line interface tool for uploading files and managing folders on WebViewer servers. Built with Rust for performance, reliability, and cross-platform compatibility.

## Features

- **File Upload**: Upload single files or multiple files to WebViewer servers
- **Directory Upload**: Upload entire directories with all contained files
- **Folder Management**: Create new folders with customizable permissions
- **Health Monitoring**: Check server health and authentication status
- **License Checking**: Monitor FLEXlm license server status and usage
- **Proxy Support**: Full support for NEGOTIATE, NTLM, and BASIC realm proxy authentication
- **SSL/TLS**: Secure connections with optional certificate validation bypass
- **Concurrent Uploads**: Efficient parallel file processing
- **Progress Tracking**: Real-time upload progress with detailed logging
- **Cross-Platform**: Works on Windows, macOS, and Linux

## Installation

### Prerequisites

- Rust 1.70+ (for building from source)
- Windows: Visual Studio Build Tools or Visual Studio Community
- License checking requires `lmutil.exe` in PATH (for license features)

### From Source

```bash
git clone https://github.com/your-org/wli.git
cd wli
cargo build --release
```

The compiled binary will be available at `target/release/wli` (or `wli.exe` on Windows).

### Binary Installation

Download the latest release from the [releases page](https://github.com/your-org/wli/releases) and place the binary in your PATH.

## Quick Start

### Basic File Upload

```bash
# Upload a single file to the root folder
wli -h https://your-server.com -U username -P password -f /path/to/file.zip

# Upload multiple files
wli -h https://your-server.com -U username -P password -f file1.zip file2.pdf file3.docx

# Upload to a specific folder by path
wli -h https://your-server.com -U username -P password -f file.zip -t "Projects/MyProject"

# Upload to a specific folder by ID
wli -h https://your-server.com -U username -P password -f file.zip -t "folder-id-123" -i
```

### Directory Upload

```bash
# Upload entire directory
wli -h https://your-server.com -U username -P password -d /path/to/directory

# Upload directory to specific target
wli -h https://your-server.com -U username -P password -d /path/to/directory -t "Projects"
```

### Folder Creation

```bash
# Create a new folder
wli -h https://your-server.com -U username -P password -n "New Folder" -t "parent-folder"

# Create folder with specific permissions (1=Manage, 2=Edit, 3=View)
wli -h https://your-server.com -U username -P password -n "New Folder" --permission-level 2
```

## Command Reference

### Required Arguments

| Option | Short | Description |
|--------|-------|-------------|
| `--host` | `-h` | WebViewer server hostname or IP address |
| `--usr` | `-U` | Username for authentication |
| `--pwd` | `-P` | Password for authentication |

### Upload Options

| Option | Short | Description |
|--------|-------|-------------|
| `--files` | `-f` | Paths to files to upload (space-separated) |
| `--dir` | `-d` | Directory path to upload |
| `--target` | `-t` | Target folder path or ID (default: "root") |
| `--use-id` | `-i` | Use folder ID instead of path for target |
| `--name` | `-n` | Create new folder with this name |
| `--permission-level` | | Folder permission level: 1=Manage, 2=Edit, 3=View (default: 3) |

### Connection Options

| Option | Short | Description |
|--------|-------|-------------|
| `--insecure` | `-k` | Disable SSL certificate validation |
| `--timeout` | | Request timeout (default: 30s, supports: ms, s, m, h) |

### Utility Options

| Option | Description |
|--------|-------------|
| `--health` | Check server health and authentication status |
| `--license` | Check FLEXlm license server status and usage |
| `--license-server` | Specify license server address (optional) |
| `--extensions` | List all supported file extensions |
| `--verbose` | `-v` | Enable verbose output for debugging |
| `--help` | Show help message |

## Examples

### Advanced Upload Scenarios

```bash
# Upload with folder creation and custom permissions
wli -h https://server.com -U user -P pass -f *.zip -n "Project Files" -t "Projects" --permission-level 2

# Upload directory with verbose logging
wli -h https://server.com -U user -P pass -d ./project-files -t "Projects/Active" -v

# Upload with custom timeout and insecure connection
wli -h https://server.com -U user -P pass -f large-file.zip --timeout 5m -k
```

### Health and License Monitoring

```bash
# Check server health
wli -h https://server.com -U user -P pass --health

# Check license status with default server
wli --license

# Check license status with specific server
wli --license --license-server <EMAIL>

# List supported file extensions
wli --extensions
```

### Proxy Configuration

For environments requiring proxy authentication, configure your system proxy settings. The tool supports:

- **BASIC realm="ADauth"**: Standard basic authentication with realm
- **NEGOTIATE**: Kerberos/NTLM negotiation
- **NTLM**: NT LAN Manager authentication

## Configuration

### Environment Variables

You can set default values using environment variables:

```bash
export WLI_HOST="https://your-default-server.com"
export WLI_USERNAME="your-username"
export WLI_PASSWORD="your-password"
```

### Supported File Extensions

Use `wli --extensions` to see the complete list of supported file extensions. Common formats include:

- Archives: `.zip`, `.rar`, `.7z`, `.tar.gz`
- Documents: `.pdf`, `.docx`, `.xlsx`, `.pptx`
- Images: `.jpg`, `.png`, `.gif`, `.svg`
- Videos: `.mp4`, `.avi`, `.mov`, `.mkv`
- And many more...

## Architecture

The CLI is built with a modern, modular architecture:

- **Core**: Configuration management and error handling
- **HTTP Client**: Enhanced HTTP client with authentication and proxy support
- **Services**: Specialized services for authentication, upload, and health checking
- **API Client**: Unified interface for all server operations
- **Utilities**: File validation, license checking, and helper functions

## Troubleshooting

### Common Issues

**Authentication Failed**
```bash
# Verify credentials and server URL
wli -h https://server.com -U user -P pass --health -v
```

**SSL Certificate Errors**
```bash
# Bypass SSL validation (not recommended for production)
wli -h https://server.com -U user -P pass -f file.zip -k
```

**File Not Supported**
```bash
# Check supported extensions
wli --extensions
```

**License Check Failed**
```bash
# Ensure lmutil.exe is in PATH and accessible
where lmutil  # Windows
which lmutil  # Linux/macOS
```

### Debug Mode

Enable verbose logging for detailed troubleshooting:

```bash
wli -h server.com -U user -P pass -f file.zip -v
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For issues and questions:

- Create an issue on [GitHub Issues](https://github.com/your-org/wli/issues)
- Check the [documentation](https://github.com/your-org/wli/wiki)
- Review the [changelog](CHANGELOG.md) for recent updates

## Performance and Limits

### Upload Performance

- **Concurrent Uploads**: Up to 4 parallel file uploads by default
- **Chunk Size**: 8KB chunks for optimal memory usage
- **Retry Logic**: Automatic retry with exponential backoff
- **Memory Efficient**: Streaming uploads for large files

### File Limits

- **File Size**: No hard limit (depends on server configuration)
- **Concurrent Files**: Configurable (default: 4)
- **Supported Extensions**: 50+ file types (use `--extensions` to see all)

## Security Features

### Authentication

- **Secure Token Management**: JWT tokens with automatic refresh
- **Session Validation**: Automatic session health checks
- **Credential Protection**: No credential storage or caching

### Network Security

- **TLS/SSL**: Secure HTTPS connections by default
- **Certificate Validation**: Full certificate chain validation
- **Proxy Authentication**: Support for enterprise proxy environments
- **Request Signing**: Secure API request authentication

## Development

### Building from Source

```bash
# Clone the repository
git clone https://github.com/your-org/wli.git
cd wli

# Build in debug mode
cargo build

# Build optimized release
cargo build --release

# Run tests
cargo test

# Run with logging
RUST_LOG=debug cargo run -- --help
```

### Project Structure

```
src/
├── main.rs              # CLI entry point
├── lib.rs               # Library exports and CLI args
├── core/                # Core types and configuration
│   ├── config.rs        # Application configuration
│   ├── error.rs         # Error types and handling
│   └── models.rs        # Data models and types
├── http/                # HTTP client infrastructure
│   ├── client.rs        # Enhanced HTTP client
│   ├── auth.rs          # Authentication handling
│   └── proxy.rs         # Proxy configuration
├── services/            # Business logic services
│   ├── auth.rs          # Authentication service
│   ├── upload.rs        # Upload service
│   └── health.rs        # Health check service
├── api/                 # API client layer
│   └── client.rs        # Unified API client
└── utils/               # Utility functions
    ├── file.rs          # File operations
    ├── license.rs       # License checking
    └── validation.rs    # Input validation
```

### Testing

```bash
# Run all tests
cargo test

# Run tests with output
cargo test -- --nocapture

# Run specific test module
cargo test utils::license

# Run integration tests
cargo test --test integration
```

## Version History

See [CHANGELOG.md](CHANGELOG.md) for detailed version history and breaking changes.

## Version

Current version: **1.5.4**

Built with ❤️ using Rust for the WebViewer Team.
