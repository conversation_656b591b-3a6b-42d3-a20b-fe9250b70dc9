//! Retry policy and logic for HTTP requests.
//! 
//! This module provides configurable retry policies with exponential backoff
//! and jitter to handle transient network failures gracefully.

use std::time::Duration;
use tracing::{debug, warn};

/// Retry policy configuration
#[derive(Debug, <PERSON>lone)]
pub struct RetryPolicy {
    /// Maximum number of retry attempts
    pub max_retries: u32,
    /// Base delay between retries
    pub delay: Duration,
    /// Maximum delay between retries
    pub max_delay: Duration,
    /// Backoff multiplier
    pub backoff_multiplier: f64,
    /// Whether to add jitter to delays
    pub use_jitter: bool,
}

impl RetryPolicy {
    /// Create a new retry policy with default settings
    pub fn new(max_retries: u32, base_delay: Duration) -> Self {
        Self {
            max_retries,
            delay: base_delay,
            max_delay: Duration::from_secs(60), // Maximum 60 seconds
            backoff_multiplier: 2.0,
            use_jitter: true,
        }
    }

    /// Create a retry policy with exponential backoff
    pub fn exponential_backoff(max_retries: u32, base_delay: Duration) -> Self {
        Self {
            max_retries,
            delay: base_delay,
            max_delay: Duration::from_secs(60),
            backoff_multiplier: 2.0,
            use_jitter: true,
        }
    }

    /// Create a retry policy with fixed delay
    pub fn fixed_delay(max_retries: u32, delay: Duration) -> Self {
        Self {
            max_retries,
            delay,
            max_delay: delay,
            backoff_multiplier: 1.0,
            use_jitter: false,
        }
    }

    /// Create a retry policy with no retries
    pub fn no_retry() -> Self {
        Self {
            max_retries: 0,
            delay: Duration::from_secs(0),
            max_delay: Duration::from_secs(0),
            backoff_multiplier: 1.0,
            use_jitter: false,
        }
    }

    /// Calculate the delay for a specific retry attempt
    pub fn calculate_delay(&self, attempt: u32) -> Duration {
        if attempt == 0 {
            return Duration::from_secs(0);
        }

        let mut delay = self.delay;
        
        // Apply exponential backoff
        if self.backoff_multiplier > 1.0 {
            let multiplier = self.backoff_multiplier.powi((attempt - 1) as i32);
            delay = Duration::from_millis((delay.as_millis() as f64 * multiplier) as u64);
        }

        // Cap at maximum delay
        if delay > self.max_delay {
            delay = self.max_delay;
        }

        // Add jitter if enabled
        if self.use_jitter {
            delay = self.add_jitter(delay);
        }

        debug!("Calculated retry delay for attempt {}: {:?}", attempt, delay);
        delay
    }

    /// Add jitter to a delay to avoid thundering herd problems
    fn add_jitter(&self, delay: Duration) -> Duration {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        // Use a simple hash-based jitter to make it deterministic but varied
        let mut hasher = DefaultHasher::new();
        std::thread::current().id().hash(&mut hasher);
        let hash = hasher.finish();
        
        // Add up to 25% jitter
        let jitter_factor = 0.75 + (hash % 250) as f64 / 1000.0;
        let jittered_millis = (delay.as_millis() as f64 * jitter_factor) as u64;
        
        Duration::from_millis(jittered_millis)
    }

    /// Check if we should retry based on the attempt number
    pub fn should_retry(&self, attempt: u32) -> bool {
        attempt <= self.max_retries
    }

    /// Get the maximum number of total attempts (including initial attempt)
    pub fn max_attempts(&self) -> u32 {
        self.max_retries + 1
    }
}

/// Retry executor that handles the retry logic
#[derive(Debug)]
pub struct RetryExecutor {
    policy: RetryPolicy,
}

impl RetryExecutor {
    /// Create a new retry executor with the given policy
    pub fn new(policy: RetryPolicy) -> Self {
        Self { policy }
    }

    /// Execute a closure with retry logic
    pub async fn execute<F, Fut, T, E>(&self, mut operation: F) -> Result<T, E>
    where
        F: FnMut() -> Fut,
        Fut: std::future::Future<Output = Result<T, E>>,
        E: std::fmt::Display,
    {
        let mut attempt = 0;
        
        loop {
            attempt += 1;
            
            debug!("Executing operation (attempt {}/{})", attempt, self.policy.max_attempts());
            
            match operation().await {
                Ok(result) => {
                    if attempt > 1 {
                        debug!("Operation succeeded after {} attempts", attempt);
                    }
                    return Ok(result);
                }
                Err(error) => {
                    if attempt >= self.policy.max_attempts() {
                        warn!("Operation failed after {} attempts: {}", attempt, error);
                        return Err(error);
                    }
                    
                    let delay = self.policy.calculate_delay(attempt);
                    warn!("Operation failed (attempt {}/{}), retrying in {:?}: {}", 
                          attempt, self.policy.max_attempts(), delay, error);
                    
                    if delay > Duration::from_secs(0) {
                        tokio::time::sleep(delay).await;
                    }
                }
            }
        }
    }

    /// Execute a closure with retry logic and custom retry condition
    pub async fn execute_with_condition<F, Fut, T, E, C>(
        &self,
        mut operation: F,
        mut should_retry: C,
    ) -> Result<T, E>
    where
        F: FnMut() -> Fut,
        Fut: std::future::Future<Output = Result<T, E>>,
        E: std::fmt::Display,
        C: FnMut(&E) -> bool,
    {
        let mut attempt = 0;
        
        loop {
            attempt += 1;
            
            debug!("Executing operation with condition (attempt {}/{})", 
                   attempt, self.policy.max_attempts());
            
            match operation().await {
                Ok(result) => {
                    if attempt > 1 {
                        debug!("Operation succeeded after {} attempts", attempt);
                    }
                    return Ok(result);
                }
                Err(error) => {
                    if attempt >= self.policy.max_attempts() || !should_retry(&error) {
                        warn!("Operation failed after {} attempts: {}", attempt, error);
                        return Err(error);
                    }
                    
                    let delay = self.policy.calculate_delay(attempt);
                    warn!("Operation failed (attempt {}/{}), retrying in {:?}: {}", 
                          attempt, self.policy.max_attempts(), delay, error);
                    
                    if delay > Duration::from_secs(0) {
                        tokio::time::sleep(delay).await;
                    }
                }
            }
        }
    }

    /// Get the retry policy
    pub fn policy(&self) -> &RetryPolicy {
        &self.policy
    }
}

/// Trait for types that can determine if they should be retried
pub trait Retryable {
    /// Check if this error should trigger a retry
    fn is_retryable(&self) -> bool;
}

impl Retryable for reqwest::Error {
    fn is_retryable(&self) -> bool {
        // Retry on timeout, connection errors, and some server errors
        self.is_timeout() || self.is_connect() || self.is_request()
    }
}

impl Retryable for crate::core::WliError {
    fn is_retryable(&self) -> bool {
        match self {
            crate::core::WliError::Network { .. } => true,
            crate::core::WliError::Server { code, .. } => {
                // Retry on 5xx server errors, but not 4xx client errors
                *code >= 500
            }
            _ => false,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_retry_policy_fixed_delay() {
        let policy = RetryPolicy::fixed_delay(3, Duration::from_millis(100));
        
        assert_eq!(policy.calculate_delay(0), Duration::from_secs(0));
        assert_eq!(policy.calculate_delay(1), Duration::from_millis(100));
        assert_eq!(policy.calculate_delay(2), Duration::from_millis(100));
        assert_eq!(policy.calculate_delay(3), Duration::from_millis(100));
    }

    #[test]
    fn test_retry_policy_exponential_backoff() {
        let policy = RetryPolicy {
            max_retries: 3,
            delay: Duration::from_millis(100),
            max_delay: Duration::from_secs(10),
            backoff_multiplier: 2.0,
            use_jitter: false,
        };
        
        assert_eq!(policy.calculate_delay(0), Duration::from_secs(0));
        assert_eq!(policy.calculate_delay(1), Duration::from_millis(100));
        assert_eq!(policy.calculate_delay(2), Duration::from_millis(200));
        assert_eq!(policy.calculate_delay(3), Duration::from_millis(400));
    }

    #[test]
    fn test_should_retry() {
        let policy = RetryPolicy::new(3, Duration::from_millis(100));
        
        assert!(policy.should_retry(0));
        assert!(policy.should_retry(1));
        assert!(policy.should_retry(2));
        assert!(policy.should_retry(3));
        assert!(!policy.should_retry(4));
    }
}
