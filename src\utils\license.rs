//! License checking utilities using lmutil.exe
//! 
//! This module provides functionality to check FLEXlm license server status
//! and parse license information including floating vs node-locked licenses
//! and license usage statistics.

use crate::utils::file::WliResult;
use std::process::Command;
use tracing::{debug, instrument, warn};
use which::which;

/// License type enumeration
#[derive(Debug, Clone, PartialEq)]
pub enum LicenseType {
    Floating,
    NodeLocked,
    Unknown,
}

/// License feature information
#[derive(Debug, <PERSON>lone)]
pub struct LicenseFeature {
    pub name: String,
    pub total_licenses: u32,
    pub licenses_in_use: u32,
    pub license_type: LicenseType,
}

/// License server information
#[derive(Debug, Clone)]
pub struct LicenseServerInfo {
    pub server_address: String,
    pub license_file_path: String,
    pub server_status: String,
    pub vendor_daemon_status: String,
    pub features: Vec<LicenseFeature>,
}

/// License checker for FLEXlm license servers
#[derive(Debug)]
pub struct LicenseChecker {
    lmutil_path: Option<String>,
}

impl LicenseChecker {
    /// Create a new license checker
    pub fn new() -> WliResult<Self> {
        let lmutil_path = Self::find_lmutil()?;
        Ok(Self { lmutil_path })
    }

    /// Find lmutil.exe in the system PATH or common locations
    fn find_lmutil() -> WliResult<Option<String>> {
        // First try to find in PATH
        if let Ok(path) = which("lmutil") {
            debug!("Found lmutil at: {}", path.display());
            return Ok(Some(path.to_string_lossy().to_string()));
        }

        // Try common installation paths on Windows
        let common_paths = [
            r"C:\Program Files\Autodesk\Network License Manager\lmutil.exe",
            r"C:\Program Files (x86)\Autodesk\Network License Manager\lmutil.exe",
            r"C:\flexlm\lmutil.exe",
            r"C:\FLEXlm\lmutil.exe",
            r"D:\Data\Softwares\FLEXlm\FLEXlm\lmutil.exe",
        ];

        for path in &common_paths {
            if std::path::Path::new(path).exists() {
                debug!("Found lmutil at: {}", path);
                return Ok(Some(path.to_string()));
            }
        }

        warn!("lmutil.exe not found in PATH or common locations");
        Ok(None)
    }

    /// Check license server status
    #[instrument(skip(self))]
    pub fn check_license_status(&self, server: Option<&str>) -> WliResult<LicenseServerInfo> {
        let lmutil_path = self.lmutil_path.as_ref()
            .ok_or_else(|| crate::utils::file::WliError::file_system("lmutil.exe not found"))?;

        let mut cmd = Command::new(lmutil_path);
        cmd.arg("lmstat");
        cmd.arg("-a");

        if let Some(server_addr) = server {
            cmd.arg("-c").arg(server_addr);
        }

        debug!("Executing: {:?}", cmd);

        let output = cmd.output()
            .map_err(|e| crate::utils::file::WliError::file_system(format!("Failed to execute lmutil: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(crate::utils::file::WliError::file_system(format!("lmutil failed: {}", stderr)));
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        debug!("lmutil output: {}", stdout);

        self.parse_license_output(&stdout)
    }

    /// Parse lmutil output to extract license information
    fn parse_license_output(&self, output: &str) -> WliResult<LicenseServerInfo> {
        let mut server_address = String::new();
        let mut license_file_path = String::new();
        let mut server_status = String::new();
        let mut vendor_daemon_status = String::new();
        let mut features = Vec::new();

        let lines: Vec<&str> = output.lines().collect();
        let mut i = 0;

        while i < lines.len() {
            let line = lines[i].trim();

            // Parse license server status
            if line.starts_with("License server status:") {
                if let Some(addr) = line.split(':').nth(1) {
                    server_address = addr.trim().to_string();
                }
            }

            // Parse license file path
            if line.contains("License file(s) on") {
                if let Some(path_part) = line.split(':').nth(2) {
                    license_file_path = path_part.trim().to_string();
                }
            }

            // Parse server status
            if line.contains("license server UP") || line.contains("license server DOWN") {
                server_status = line.to_string();
            }

            // Parse vendor daemon status
            if line.contains("Vendor daemon status") {
                vendor_daemon_status = line.to_string();
            }

            // Parse feature usage
            if line.starts_with("Users of ") {
                if let Some(feature) = self.parse_feature_line(line) {
                    features.push(feature);
                }
            }

            i += 1;
        }

        Ok(LicenseServerInfo {
            server_address,
            license_file_path,
            server_status,
            vendor_daemon_status,
            features,
        })
    }

    /// Parse a single feature usage line
    fn parse_feature_line(&self, line: &str) -> Option<LicenseFeature> {
        // Example: "Users of Star:  (Total of 350 licenses issued;  Total of 0 licenses in use)"
        let parts: Vec<&str> = line.split(':').collect();
        if parts.len() != 2 {
            return None;
        }

        let feature_name = parts[0].replace("Users of ", "").trim().to_string();
        let usage_part = parts[1];

        // Extract total licenses - find first occurrence
        let total_licenses = if let Some(start) = usage_part.find("Total of ") {
            let after_total = &usage_part[start + 9..]; // Skip "Total of "
            if let Some(end) = after_total.find(" license") {
                let num_str = &after_total[..end].trim();
                num_str.parse().unwrap_or(0)
            } else {
                0
            }
        } else {
            0
        };

        // Extract licenses in use - find second occurrence
        let licenses_in_use = if let Some(first_total) = usage_part.find("Total of ") {
            let after_first = &usage_part[first_total + 9..];
            if let Some(second_total_rel) = after_first.find("Total of ") {
                let second_total = first_total + 9 + second_total_rel;
                let after_second = &usage_part[second_total + 9..]; // Skip "Total of "
                if let Some(end) = after_second.find(" license") {
                    let num_str = &after_second[..end].trim();
                    num_str.parse().unwrap_or(0)
                } else {
                    0
                }
            } else {
                0
            }
        } else {
            0
        };

        // Determine license type (simplified heuristic)
        let license_type = if total_licenses > 1 {
            LicenseType::Floating
        } else if total_licenses == 1 {
            LicenseType::NodeLocked
        } else {
            LicenseType::Unknown
        };

        Some(LicenseFeature {
            name: feature_name,
            total_licenses,
            licenses_in_use,
            license_type,
        })
    }

    /// Count license "stars" (total available licenses across all features)
    pub fn count_license_stars(&self, info: &LicenseServerInfo) -> u32 {
        info.features.iter().map(|f| f.total_licenses).sum()
    }

    /// Get license summary as formatted string
    pub fn format_license_summary(&self, info: &LicenseServerInfo) -> String {
        let mut summary = String::new();
        
        summary.push_str(&format!("License Server: {}\n", info.server_address));
        summary.push_str(&format!("License File: {}\n", info.license_file_path));
        summary.push_str(&format!("Server Status: {}\n", info.server_status));
        summary.push_str(&format!("Vendor Daemon: {}\n\n", info.vendor_daemon_status));

        summary.push_str("Feature Usage:\n");
        for feature in &info.features {
            let type_str = match feature.license_type {
                LicenseType::Floating => "Floating",
                LicenseType::NodeLocked => "Node-locked",
                LicenseType::Unknown => "Unknown",
            };
            
            summary.push_str(&format!(
                "  {} ({}): {}/{} licenses in use\n",
                feature.name,
                type_str,
                feature.licenses_in_use,
                feature.total_licenses
            ));
        }

        let total_stars = self.count_license_stars(info);
        summary.push_str(&format!("\nTotal License Stars: {}\n", total_stars));

        summary
    }
}

impl Default for LicenseChecker {
    fn default() -> Self {
        Self::new().unwrap_or_else(|_| Self { lmutil_path: None })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_license_feature_parsing() {
        let checker = LicenseChecker::default();

        // Test parsing a typical feature line
        let line = "Users of Star:  (Total of 350 licenses issued;  Total of 0 licenses in use)";
        let feature = checker.parse_feature_line(line).unwrap();

        assert_eq!(feature.name, "Star");
        assert_eq!(feature.total_licenses, 350);
        assert_eq!(feature.licenses_in_use, 0);
        assert_eq!(feature.license_type, LicenseType::Floating);
    }

    #[test]
    fn test_node_locked_license_detection() {
        let checker = LicenseChecker::default();

        // Test parsing a node-locked license
        let line = "Users of JPT_WEBVIEWER:  (Total of 1 license issued;  Total of 0 licenses in use)";
        let feature = checker.parse_feature_line(line).unwrap();

        assert_eq!(feature.name, "JPT_WEBVIEWER");
        assert_eq!(feature.total_licenses, 1);
        assert_eq!(feature.licenses_in_use, 0);
        assert_eq!(feature.license_type, LicenseType::NodeLocked);
    }

    #[test]
    fn test_license_stars_counting() {
        let checker = LicenseChecker::default();

        let info = LicenseServerInfo {
            server_address: "27000@test".to_string(),
            license_file_path: "/test/path".to_string(),
            server_status: "UP".to_string(),
            vendor_daemon_status: "UP".to_string(),
            features: vec![
                LicenseFeature {
                    name: "Star".to_string(),
                    total_licenses: 350,
                    licenses_in_use: 0,
                    license_type: LicenseType::Floating,
                },
                LicenseFeature {
                    name: "JPT_WEBVIEWER".to_string(),
                    total_licenses: 1,
                    licenses_in_use: 0,
                    license_type: LicenseType::NodeLocked,
                },
                LicenseFeature {
                    name: "JPT_WVNUM".to_string(),
                    total_licenses: 10,
                    licenses_in_use: 0,
                    license_type: LicenseType::Floating,
                },
            ],
        };

        let total_stars = checker.count_license_stars(&info);
        assert_eq!(total_stars, 361); // 350 + 1 + 10
    }
}
