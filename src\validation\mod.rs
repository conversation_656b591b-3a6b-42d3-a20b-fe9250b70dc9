use std::path::Path;
use anyhow::Result;
use crate::utils::is_valid_name;

pub fn validate_file_exists(file_path: &str) -> Result<()> {
    if !Path::new(file_path).is_file() {
        return Err(anyhow::anyhow!("File does not exist"));
    }

    if !is_valid_name(file_path) {
        return Err(anyhow::anyhow!("File extension is not supported"));
    }

    Ok(())
}

pub fn validate_folder_exists(folder_path: &str) -> Result<()> {
    if !Path::new(folder_path).is_dir() {
        return Err(anyhow::anyhow!("Folder does not exist"));
    }

    Ok(())
}
