//! Core domain models and data structures.
//! 
//! This module contains the fundamental data types used throughout
//! the application for representing files, uploads, and server responses.

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use uuid::Uuid;

/// Represents a file to be uploaded
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UploadFile {
    /// Unique identifier for the file
    pub id: Uuid,
    /// Local file path
    pub path: PathBuf,
    /// File name (extracted from path)
    pub name: String,
    /// File size in bytes
    pub size: u64,
    /// MIME type of the file
    pub mime_type: String,
    /// File extension
    pub extension: String,
    /// Upload status
    pub status: UploadStatus,
    /// Creation timestamp
    pub created_at: DateTime<Utc>,
}

/// Upload status enumeration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum UploadStatus {
    /// File is pending upload
    Pending,
    /// File is currently being uploaded
    InProgress { progress: f64 },
    /// File upload completed successfully
    Completed { server_id: String },
    /// File upload failed
    Failed { error: String },
}

/// Represents an upload session
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UploadSession {
    /// Session identifier
    pub id: Uuid,
    /// Target folder path or ID
    pub target: String,
    /// Whether target is an ID or path
    pub use_target_id: bool,
    /// Files to upload
    pub files: Vec<UploadFile>,
    /// Session status
    pub status: SessionStatus,
    /// Session creation time
    pub created_at: DateTime<Utc>,
    /// Session completion time
    pub completed_at: Option<DateTime<Utc>>,
}

/// Session status enumeration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum SessionStatus {
    /// Session is being prepared
    Preparing,
    /// Session is active and uploading
    Active,
    /// Session completed successfully
    Completed,
    /// Session failed
    Failed { error: String },
    /// Session was cancelled
    Cancelled,
}

/// User information from server
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    /// User ID
    pub id: String,
    /// Username
    pub username: String,
    /// User email
    pub email: Option<String>,
    /// User display name
    pub display_name: Option<String>,
    /// User roles
    pub roles: Vec<String>,
}

/// Authentication token information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthToken {
    /// JWT token string
    pub token: String,
    /// Token expiration time
    pub expires_at: DateTime<Utc>,
    /// Token type (Bearer, etc.)
    pub token_type: String,
}

/// Server health information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerHealth {
    /// Server status
    pub status: String,
    /// Server version
    pub version: Option<String>,
    /// Response time in milliseconds
    pub response_time_ms: u64,
    /// Additional server information
    pub info: Option<serde_json::Value>,
}

/// Folder creation request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateFolderRequest {
    /// Folder name
    pub name: String,
    /// Parent folder ID or path
    pub parent: String,
    /// Whether parent is an ID
    pub use_parent_id: bool,
    /// Default permissions
    pub permissions: FolderPermissions,
}

/// Folder permissions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FolderPermissions {
    /// Permission level (1: Manage, 2: Edit, 3: View)
    pub level: i8,
    /// Team members with access
    pub team_members: Vec<TeamMember>,
}

/// Team member information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeamMember {
    /// Member ID
    pub id: String,
    /// Permission level for this member
    pub permission: i8,
}

/// Upload progress information
#[derive(Debug, Clone)]
pub struct UploadProgress {
    /// File being uploaded
    pub file_id: Uuid,
    /// Bytes uploaded so far
    pub bytes_uploaded: u64,
    /// Total bytes to upload
    pub total_bytes: u64,
    /// Upload speed in bytes per second
    pub speed_bps: f64,
    /// Estimated time remaining in seconds
    pub eta_seconds: Option<u64>,
}

impl UploadFile {
    /// Create a new upload file from a path
    pub fn new(path: PathBuf) -> crate::core::WliResult<Self> {
        let metadata = std::fs::metadata(&path)?;
        let name = path
            .file_name()
            .and_then(|n| n.to_str())
            .ok_or_else(|| crate::core::WliError::file_validation("Invalid file name"))?
            .to_string();
        
        let extension = path
            .extension()
            .and_then(|e| e.to_str())
            .unwrap_or("")
            .to_string();
        
        let mime_type = mime_guess::from_path(&path)
            .first_or_octet_stream()
            .to_string();

        Ok(Self {
            id: Uuid::new_v4(),
            path,
            name,
            size: metadata.len(),
            mime_type,
            extension,
            status: UploadStatus::Pending,
            created_at: Utc::now(),
        })
    }

    /// Check if the file has a supported extension
    pub fn is_supported(&self) -> bool {
        crate::utils::is_supported_extension(&self.extension)
    }

    /// Get upload progress percentage
    pub fn progress_percentage(&self) -> f64 {
        match &self.status {
            UploadStatus::InProgress { progress } => *progress,
            UploadStatus::Completed { .. } => 100.0,
            _ => 0.0,
        }
    }
}

impl UploadSession {
    /// Create a new upload session
    pub fn new(target: String, use_target_id: bool) -> Self {
        Self {
            id: Uuid::new_v4(),
            target,
            use_target_id,
            files: Vec::new(),
            status: SessionStatus::Preparing,
            created_at: Utc::now(),
            completed_at: None,
        }
    }

    /// Add a file to the session
    pub fn add_file(&mut self, file: UploadFile) {
        self.files.push(file);
    }

    /// Get total session progress percentage
    pub fn progress_percentage(&self) -> f64 {
        if self.files.is_empty() {
            return 0.0;
        }

        let total_progress: f64 = self.files.iter()
            .map(|f| f.progress_percentage())
            .sum();
        
        total_progress / self.files.len() as f64
    }

    /// Check if session is complete
    pub fn is_complete(&self) -> bool {
        matches!(self.status, SessionStatus::Completed)
    }

    /// Mark session as completed
    pub fn mark_completed(&mut self) {
        self.status = SessionStatus::Completed;
        self.completed_at = Some(Utc::now());
    }
}
