//! Enhanced HTTP client with comprehensive authentication and proxy support.

use crate::core::{App<PERSON>onfig, Wli<PERSON><PERSON><PERSON>, Wli<PERSON><PERSON><PERSON>};
use crate::http::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Proxy<PERSON><PERSON><PERSON>, RetryPolicy};
use reqwest::{Client, ClientBuilder, Request, Response};
use serde_json::Value;
use std::time::Duration;
use tracing::{debug, instrument, warn};

/// Enhanced HTTP client with authentication and proxy support
#[derive(Debug, Clone)]
pub struct HttpClient {
    client: Client,
    config: AppConfig,
    auth_handler: Auth<PERSON><PERSON><PERSON>,
    proxy_handler: Option<ProxyHandler>,
    retry_policy: RetryPolicy,
}

impl HttpClient {
    /// Create a new HTTP client with the given configuration
    pub fn new(config: AppConfig) -> WliResult<Self> {
        let mut builder = ClientBuilder::new()
            .timeout(Duration::from_secs(config.server.timeout))
            .danger_accept_invalid_certs(config.server.insecure)
            .gzip(true)
            .brotli(true)
            .user_agent(format!("wli/{}", env!("CARGO_PKG_VERSION")));

        // Configure proxy if specified
        let proxy_handler = if let Some(proxy_config) = &config.proxy {
            let proxy_handler = ProxyHandler::new(proxy_config.clone())?;
            builder = proxy_handler.configure_client(builder)?;
            Some(proxy_handler)
        } else {
            None
        };

        let client = builder.build().map_err(|e| {
            WliError::network(format!("Failed to create HTTP client: {}", e))
        })?;

        let auth_handler = AuthHandler::new(config.auth.clone());
        let retry_policy = RetryPolicy::new(
            config.server.max_retries,
            Duration::from_millis(config.server.retry_delay_ms),
        );

        Ok(Self {
            client,
            config,
            auth_handler,
            proxy_handler,
            retry_policy,
        })
    }

    /// Execute a GET request
    #[instrument(skip(self), fields(url = %url))]
    pub async fn get(&self, url: &str) -> WliResult<Response> {
        let request = self.client.get(url);
        self.execute_request(request).await
    }

    /// Execute a POST request with JSON body
    #[instrument(skip(self, body), fields(url = %url))]
    pub async fn post_json(&self, url: &str, body: &Value) -> WliResult<Response> {
        let request = self.client.post(url).json(body);
        self.execute_request(request).await
    }

    /// Execute a POST request with form data
    #[instrument(skip(self, form), fields(url = %url))]
    pub async fn post_form<T>(&self, url: &str, form: &T) -> WliResult<Response>
    where
        T: serde::Serialize,
    {
        let request = self.client.post(url).form(form);
        self.execute_request(request).await
    }

    /// Execute a POST request with multipart form data
    #[instrument(skip(self, form), fields(url = %url))]
    pub async fn post_multipart(
        &self,
        url: &str,
        form: reqwest::multipart::Form,
    ) -> WliResult<Response> {
        let request = self.client.post(url).multipart(form);
        self.execute_request(request).await
    }

    /// Execute a PUT request with JSON body
    #[instrument(skip(self, body), fields(url = %url))]
    pub async fn put_json(&self, url: &str, body: &Value) -> WliResult<Response> {
        let request = self.client.put(url).json(body);
        self.execute_request(request).await
    }

    /// Execute a DELETE request
    #[instrument(skip(self), fields(url = %url))]
    pub async fn delete(&self, url: &str) -> WliResult<Response> {
        let request = self.client.delete(url);
        self.execute_request(request).await
    }

    /// Execute a request with authentication and retry logic
    async fn execute_request(&self, request_builder: reqwest::RequestBuilder) -> WliResult<Response> {
        // Apply authentication
        let request_builder = self.auth_handler.apply_auth(request_builder).await?;

        // Build the request
        let request = request_builder.build().map_err(|e| {
            WliError::network(format!("Failed to build request: {}", e))
        })?;

        // Execute with retry logic
        self.execute_with_retry(request).await
    }

    /// Execute request with retry logic
    async fn execute_with_retry(&self, request: Request) -> WliResult<Response> {
        let mut attempts = 0;
        let max_attempts = self.retry_policy.max_retries + 1;

        loop {
            attempts += 1;
            
            // Clone the request for retry attempts
            let request_clone = request.try_clone().ok_or_else(|| {
                WliError::network("Cannot retry request with streaming body")
            })?;

            debug!("Executing request (attempt {}/{}): {} {}", 
                   attempts, max_attempts, request_clone.method(), request_clone.url());

            match self.client.execute(request_clone).await {
                Ok(response) => {
                    let status = response.status();
                    
                    if status.is_success() {
                        debug!("Request successful: {}", status);
                        return Ok(response);
                    } else if status.is_client_error() {
                        // Don't retry client errors (4xx)
                        return Err(WliError::server(
                            status.as_u16(),
                            format!("Client error: {}", status),
                        ));
                    } else if status.is_server_error() && attempts < max_attempts {
                        // Retry server errors (5xx)
                        warn!("Server error {}, retrying in {:?} (attempt {}/{})", 
                              status, self.retry_policy.delay, attempts, max_attempts);
                        
                        tokio::time::sleep(self.retry_policy.delay).await;
                        continue;
                    } else {
                        return Err(WliError::server(
                            status.as_u16(),
                            format!("Server error: {}", status),
                        ));
                    }
                }
                Err(e) if attempts < max_attempts && self.is_retryable_error(&e) => {
                    warn!("Network error, retrying in {:?} (attempt {}/{}): {}", 
                          self.retry_policy.delay, attempts, max_attempts, e);
                    
                    tokio::time::sleep(self.retry_policy.delay).await;
                    continue;
                }
                Err(e) => {
                    return Err(WliError::from(e));
                }
            }
        }
    }

    /// Check if an error is retryable
    fn is_retryable_error(&self, error: &reqwest::Error) -> bool {
        error.is_timeout() || error.is_connect() || error.is_request()
    }

    /// Get the underlying reqwest client
    pub fn inner(&self) -> &Client {
        &self.client
    }

    /// Get the configuration
    pub fn config(&self) -> &AppConfig {
        &self.config
    }

    /// Update authentication token
    pub async fn update_auth_token(&mut self, token: String) -> WliResult<()> {
        self.auth_handler.set_token(token).await
    }

    /// Clear authentication token
    pub async fn clear_auth_token(&mut self) -> WliResult<()> {
        self.auth_handler.clear_token().await
    }

    /// Check if client has valid authentication
    pub async fn is_authenticated(&self) -> bool {
        self.auth_handler.is_authenticated().await
    }



    /// Perform health check against the server
    #[instrument(skip(self))]
    pub async fn health_check(&self, base_url: &str) -> WliResult<crate::core::ServerHealth> {
        let start_time = std::time::Instant::now();
        let health_url = format!("{}/api/health", base_url.trim_end_matches('/'));
        
        let response = self.get(&health_url).await?;
        let response_time_ms = start_time.elapsed().as_millis() as u64;
        
        if response.status().is_success() {
            let health_data: Value = response.json().await.map_err(|e| {
                WliError::network(format!("Failed to parse health response: {}", e))
            })?;

            Ok(crate::core::ServerHealth {
                status: health_data.get("status")
                    .and_then(|s| s.as_str())
                    .unwrap_or("unknown")
                    .to_string(),
                version: health_data.get("version")
                    .and_then(|v| v.as_str())
                    .map(String::from),
                response_time_ms,
                info: Some(health_data),
            })
        } else {
            Err(WliError::server(
                response.status().as_u16(),
                "Health check failed".to_string(),
            ))
        }
    }
}
