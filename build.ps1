# WebViewer CLI Build Script with Auto-Versioning
# PowerShell version for better reliability and cross-platform support

param(
    [switch]$Increment,
    [switch]$Minor,
    [switch]$Major,
    [switch]$Patch,
    [switch]$Debug,
    [switch]$Release = $true,
    [switch]$Help,
    [string]$CustomVersion,
    [switch]$Clean,
    [switch]$Test
)

# Show help
if ($Help) {
    Write-Host "WebViewer CLI Build Script" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\build.ps1 [OPTIONS]" -ForegroundColor White
    Write-Host ""
    Write-Host "Version Options:" -ForegroundColor Yellow
    Write-Host "  -Increment, -Minor    Increment minor version (1.5.4 -> 1.6.0)" -ForegroundColor White
    Write-Host "  -Major               Increment major version (1.5.4 -> 2.0.0)" -ForegroundColor White
    Write-Host "  -Patch               Increment patch version (1.5.4 -> 1.5.5)" -ForegroundColor White
    Write-Host "  -CustomVersion <ver> Set specific version (e.g., '2.0.0')" -ForegroundColor White
    Write-Host ""
    Write-Host "Build Options:" -ForegroundColor Yellow
    Write-Host "  -Debug               Build in debug mode" -ForegroundColor White
    Write-Host "  -Release             Build in release mode (default)" -ForegroundColor White
    Write-Host "  -Clean               Clean build artifacts before building" -ForegroundColor White
    Write-Host "  -Test                Run tests before building" -ForegroundColor White
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\build.ps1                    # Build with current version" -ForegroundColor Gray
    Write-Host "  .\build.ps1 -Increment         # Build and increment minor version" -ForegroundColor Gray
    Write-Host "  .\build.ps1 -Major             # Build and increment major version" -ForegroundColor Gray
    Write-Host "  .\build.ps1 -Debug -Test       # Run tests and build debug version" -ForegroundColor Gray
    Write-Host "  .\build.ps1 -CustomVersion 2.0.0  # Set specific version and build" -ForegroundColor Gray
    exit 0
}

# Function to read current version from Cargo.toml
function Get-CurrentVersion {
    $cargoContent = Get-Content "Cargo.toml" -Raw
    if ($cargoContent -match 'version\s*=\s*"([0-9]+\.[0-9]+\.[0-9]+)"') {
        return $matches[1]
    }
    throw "Version not found in Cargo.toml"
}

# Function to update version in Cargo.toml
function Set-Version {
    param([string]$NewVersion)
    
    $cargoContent = Get-Content "Cargo.toml" -Raw
    $updatedContent = $cargoContent -replace 'version\s*=\s*"[0-9]+\.[0-9]+\.[0-9]+"', "version = `"$NewVersion`""
    Set-Content "Cargo.toml" $updatedContent -NoNewline
    
    Write-Host "✓ Updated Cargo.toml version to: $NewVersion" -ForegroundColor Green
}

# Function to increment version
function Get-IncrementedVersion {
    param(
        [string]$CurrentVersion,
        [string]$IncrementType
    )
    
    $parts = $CurrentVersion.Split('.')
    $major = [int]$parts[0]
    $minor = [int]$parts[1]
    $patch = [int]$parts[2]
    
    switch ($IncrementType) {
        "major" { 
            $major++
            $minor = 0
            $patch = 0
        }
        "minor" { 
            $minor++
            $patch = 0
        }
        "patch" { 
            $patch++
        }
    }
    
    return "$major.$minor.$patch"
}

# Function to validate version format
function Test-VersionFormat {
    param([string]$Version)
    return $Version -match '^[0-9]+\.[0-9]+\.[0-9]+$'
}

# Main build process
try {
    Write-Host "🚀 WebViewer CLI Build Process" -ForegroundColor Cyan
    Write-Host "================================" -ForegroundColor Cyan
    
    # Get current version
    $currentVersion = Get-CurrentVersion
    Write-Host "📦 Current version: $currentVersion" -ForegroundColor Blue
    
    # Handle version changes
    $newVersion = $currentVersion
    
    if ($CustomVersion) {
        if (-not (Test-VersionFormat $CustomVersion)) {
            throw "Invalid version format: $CustomVersion. Use format: major.minor.patch (e.g., 1.5.4)"
        }
        $newVersion = $CustomVersion
        Set-Version $newVersion
    }
    elseif ($Major) {
        $newVersion = Get-IncrementedVersion $currentVersion "major"
        Set-Version $newVersion
    }
    elseif ($Increment -or $Minor) {
        $newVersion = Get-IncrementedVersion $currentVersion "minor"
        Set-Version $newVersion
    }
    elseif ($Patch) {
        $newVersion = Get-IncrementedVersion $currentVersion "patch"
        Set-Version $newVersion
    }
    
    if ($newVersion -ne $currentVersion) {
        Write-Host "🔄 Version changed: $currentVersion → $newVersion" -ForegroundColor Yellow
    }
    
    # Clean if requested
    if ($Clean) {
        Write-Host "🧹 Cleaning build artifacts..." -ForegroundColor Yellow
        & cargo clean
        if ($LASTEXITCODE -ne 0) {
            throw "Clean failed"
        }
        Write-Host "✓ Clean completed" -ForegroundColor Green
    }
    
    # Run tests if requested
    if ($Test) {
        Write-Host "🧪 Running tests..." -ForegroundColor Yellow
        & cargo test
        if ($LASTEXITCODE -ne 0) {
            throw "Tests failed"
        }
        Write-Host "✓ All tests passed" -ForegroundColor Green
    }
    
    # Determine build mode
    $buildMode = if ($Debug) { "debug" } else { "release" }
    $targetDir = if ($Debug) { "target/debug" } else { "target/release" }
    
    Write-Host "🔨 Building in $buildMode mode..." -ForegroundColor Yellow
    
    # Build the project
    $buildArgs = @("build")
    if (-not $Debug) {
        $buildArgs += "--release"
    }
    
    & cargo @buildArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    
    # Create versioned binary
    $binaryName = if ($Debug) { "wli-$newVersion-debug.exe" } else { "wli-$newVersion.exe" }
    $sourcePath = "$targetDir/wli.exe"
    $targetPath = "$targetDir/$binaryName"
    
    if (Test-Path $sourcePath) {
        Copy-Item $sourcePath $targetPath -Force
        
        # Get file size
        $fileSize = (Get-Item $targetPath).Length
        $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
        
        Write-Host ""
        Write-Host "🎉 Build Successful!" -ForegroundColor Green
        Write-Host "===================" -ForegroundColor Green
        Write-Host "📋 Version: $newVersion" -ForegroundColor White
        Write-Host "🔧 Build Mode: $buildMode" -ForegroundColor White
        Write-Host "📁 Output: $targetPath" -ForegroundColor White
        Write-Host "📊 File Size: $fileSizeMB MB ($fileSize bytes)" -ForegroundColor White
        Write-Host "🔗 Original: $sourcePath" -ForegroundColor White
        Write-Host ""
        
        # Show next steps
        Write-Host "💡 Next Steps:" -ForegroundColor Cyan
        Write-Host "  • Test the binary: $targetPath --help" -ForegroundColor Gray
        Write-Host "  • Run health check: $targetPath --health" -ForegroundColor Gray
        Write-Host "  • Check extensions: $targetPath --extensions" -ForegroundColor Gray
        
    } else {
        throw "Build output not found at: $sourcePath"
    }
    
} catch {
    Write-Host ""
    Write-Host "❌ Build Failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
