pub fn remove_port_if_localhost(url: &str) -> String {
    let url = url::Url::parse(url).unwrap();
    let url = if url.host_str() == Some("localhost") || url.host_str() == Some("127.0.0.1") {
        format!("{}://{}{}", url.scheme(), url.host_str().unwrap(), url.path())
    } else {
        url.to_string()
    };

    // trim the trailing slash from the URL
    url.trim_end_matches('/').to_string()
}