use anyhow::{Context, Result};
use log::debug;
use reqwest::{Body, Client};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::time::Duration;
use crate::utils::remove_port_if_localhost;
use reqwest::multipart::Form;

#[derive(Debug, Clone)]
pub struct Endpoint {
    client: Client,
    pub host: String,
    username: String,
    password: String,
    pub token: Option<String>,
    pub user: Option<User>,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct User {
    pub id: String,
    // Add other user fields as needed
}

#[derive(Debug)]
pub enum RequestData {
    Json(Value),
    Form(Vec<(String, String)>),
    MultiPart(Form),
    Body(Body),
}

impl Endpoint {
    pub fn new(host: &str, username: &str, password: &str, verify: bool, timeout: u64) -> Self {
        let client = Client::builder()
            .danger_accept_invalid_certs(!verify)
            .timeout(Duration::from_secs(timeout))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            host: remove_port_if_localhost(host),
            username: username.to_string(),
            password: password.to_string(),
            token: None,
            user: None,
        }
    }

    pub async fn do_auth(&mut self) -> Result<()> {
        let auth_login_endpoint = format!("{}/api/auth/login", self.host);
        let response = self.client
            .post(&auth_login_endpoint)
            .form(&[("username", &self.username), ("password", &self.password)])
            .send()
            .await?;

        let response_data: Value = response.json().await?;

        if response_data["code"] == 200 {
            self.token = Some(response_data["data"]["token"].as_str().unwrap().to_string());
            self.user = Some(serde_json::from_value(response_data["data"]["user"].clone())?);
            Ok(())
        } else {
            Err(anyhow::anyhow!("Authentication failed: {:?}", response_data))
        }
    }

    pub async fn do_logout(&mut self) -> Result<()> {
        if self.user.is_none() || self.token.is_none() {
            return Err(anyhow::anyhow!("ERROR: Not authenticated!"));
        }

        let endpoint = "/api/auth/logout";
        let data = serde_json::json!({
            "id": self.user.as_ref().unwrap().id
        });

        let response_data = self.post(endpoint, Some(RequestData::Json(data))).await?;

        if response_data["code"] != 200 {
            return Err(anyhow::anyhow!("Logout failed: {:?}", response_data));
        }

        self.user = None;
        self.token = None;
        Ok(())
    }

    pub async fn request(&self, method: &str, endpoint: &str, data: Option<RequestData>, params: Option<&Value>) -> anyhow::Result<Value> {
        let url = format!("{}{}", self.host, endpoint);
        let mut request = self.client.request(method.parse()?, &url);

        if let Some(token) = &self.token {
            request = request.header("Authorization", format!("Bearer {}", token));
        }


        if let Some(data) = data {
            match data {
                RequestData::Json(json_data) => {
                    request = request.json(&json_data);
                },
                RequestData::Form(form_data) => {
                    request = request.form(&form_data);
                },
                RequestData::MultiPart(part) => {
                    request = request.multipart(part);
                },
                RequestData::Body(body) => {
                    request = request.body(body);
                },
            }
        }

        if let Some(query_params) = params{
            request = request.query(query_params);
        }

        let response = request.send().await.context("Request failed")?;
        debug!("Response: {:?}", response);
        if response.status().is_server_error() {
            panic!("Unable to access server. Please try again.");
        }

        let response_data: Value = response.json().await.context("Authentication failure or unable to access server. Please try again.")?;
        Ok(response_data)
    }

    pub async fn get(&self, endpoint: &str, params: Option<&Value>) -> anyhow::Result<Value> {
        self.request("GET", endpoint, None, params).await
    }

    pub async fn post(&self, endpoint: &str, data: Option<RequestData>) -> anyhow::Result<Value> {
        self.request("POST", endpoint, data, None).await
    }

    pub async fn put(&self, endpoint: &str, data: Option<RequestData>) -> anyhow::Result<Value> {
        self.request("PUT", endpoint, data, None).await
    }
    
    pub async fn check_health(&self) -> Result<(Option<Value>, bool)> {
        let health_result = self.get("/api/health", None).await?;
        let is_live = self.is_server_live(&health_result);
        Ok((Some(health_result), is_live))
    }

    fn is_server_live(&self, health_result: &Value) -> bool {
        health_result.get("code").and_then(|c| c.as_u64()) == Some(200) &&
        health_result.get("status").and_then(|s| s.as_str()) == Some("success") &&
        health_result.get("data")
            .and_then(|d| d.get("status"))
            .and_then(|s| s.as_str()) == Some("OK")
    }

    pub async fn check_auth(&mut self, logout_after_checking: bool) -> Result<bool> {
        match self.do_auth().await {
            Ok(_) => {
                if logout_after_checking {
                    self.do_logout().await?;
                }
                Ok(true)
            },
            Err(e) => {
                debug!("Authentication failed: {}", e);
                Ok(false)
            }
        }
    }
}