//! Proxy authentication and configuration support.
//! 
//! This module provides comprehensive proxy support including
//! BASIC, NEGOTIATE, and NTLM authentication methods.

use crate::core::{ProxyAuthMethod, ProxyConfig, WliError, WliR<PERSON>ult};
use base64::Engine;
use reqwest::{C<PERSON><PERSON><PERSON><PERSON>, Proxy};
use std::collections::HashMap;
use tracing::{debug, instrument, warn};

/// Proxy handler for configuring and authenticating with proxy servers
#[derive(Debug, Clone)]
pub struct ProxyHandler {
    config: ProxyConfig,
    auth_cache: Option<String>,
}

impl ProxyHandler {
    /// Create a new proxy handler
    pub fn new(config: ProxyConfig) -> WliResult<Self> {
        // Validate proxy configuration
        if config.url.is_empty() {
            return Err(WliError::proxy_auth("Proxy URL cannot be empty"));
        }

        // Validate authentication requirements
        match config.auth_method {
            ProxyAuthMethod::Basic => {
                if config.username.is_none() || config.password.is_none() {
                    return Err(WliError::proxy_auth(
                        "Username and password required for Basic proxy authentication"
                    ));
                }
            }
            ProxyAuthMethod::Ntlm => {
                if config.username.is_none() || config.password.is_none() {
                    return Err(WliError::proxy_auth(
                        "Username and password required for NTLM proxy authentication"
                    ));
                }
            }
            ProxyAuthMethod::Negotiate | ProxyAuthMethod::None => {
                // These methods don't require explicit credentials
            }
        }

        Ok(Self {
            config,
            auth_cache: None,
        })
    }

    /// Configure the HTTP client builder with proxy settings
    #[instrument(skip(self, builder))]
    pub fn configure_client(&self, mut builder: ClientBuilder) -> WliResult<ClientBuilder> {
        debug!("Configuring proxy: {}", self.config.url);

        // Create the proxy
        let mut proxy = Proxy::all(&self.config.url).map_err(|e| {
            WliError::proxy_auth(format!("Invalid proxy URL '{}': {}", self.config.url, e))
        })?;

        // Configure proxy authentication
        match self.config.auth_method {
            ProxyAuthMethod::Basic => {
                if let (Some(username), Some(password)) = (&self.config.username, &self.config.password) {
                    debug!("Configuring Basic proxy authentication for user: {}", username);
                    proxy = proxy.basic_auth(username, password);
                }
            }
            ProxyAuthMethod::Negotiate => {
                debug!("NEGOTIATE proxy authentication will be handled by the system");
                // NEGOTIATE authentication is typically handled automatically by the system
                // Additional configuration may be needed for specific environments
            }
            ProxyAuthMethod::Ntlm => {
                debug!("NTLM proxy authentication configured");
                // NTLM authentication may require additional headers or custom handling
                // For now, we'll use basic auth as a fallback
                if let (Some(username), Some(password)) = (&self.config.username, &self.config.password) {
                    warn!("Using Basic auth as fallback for NTLM proxy authentication");
                    proxy = proxy.basic_auth(username, password);
                }
            }
            ProxyAuthMethod::None => {
                debug!("No proxy authentication configured");
            }
        }

        builder = builder.proxy(proxy);
        Ok(builder)
    }

    /// Handle proxy authentication challenge
    #[instrument(skip(self))]
    pub async fn handle_auth_challenge(&mut self, challenge: &str) -> WliResult<Option<String>> {
        debug!("Handling proxy authentication challenge: {}", challenge);

        let challenge_lower = challenge.to_lowercase();

        if challenge_lower.contains("basic") && self.config.auth_method == ProxyAuthMethod::Basic {
            return self.create_basic_auth_header();
        }

        if challenge_lower.contains("negotiate") && self.config.auth_method == ProxyAuthMethod::Negotiate {
            return self.create_negotiate_auth_header().await;
        }

        if challenge_lower.contains("ntlm") && self.config.auth_method == ProxyAuthMethod::Ntlm {
            return self.create_ntlm_auth_header().await;
        }

        warn!("Unsupported proxy authentication challenge: {}", challenge);
        Ok(None)
    }

    /// Create Basic authentication header
    fn create_basic_auth_header(&self) -> WliResult<Option<String>> {
        if let (Some(username), Some(password)) = (&self.config.username, &self.config.password) {
            let credentials = format!("{}:{}", username, password);
            let encoded = base64::engine::general_purpose::STANDARD.encode(credentials.as_bytes());
            Ok(Some(format!("Basic {}", encoded)))
        } else {
            Err(WliError::proxy_auth("Username and password required for Basic proxy authentication"))
        }
    }

    /// Create NEGOTIATE authentication header
    async fn create_negotiate_auth_header(&mut self) -> WliResult<Option<String>> {
        #[cfg(windows)]
        {
            // On Windows, use SSPI for NEGOTIATE authentication
            self.create_windows_negotiate_header().await
        }
        #[cfg(not(windows))]
        {
            // On non-Windows systems, NEGOTIATE might not be available
            warn!("NEGOTIATE proxy authentication not supported on this platform");
            Ok(None)
        }
    }

    /// Create NTLM authentication header
    async fn create_ntlm_auth_header(&mut self) -> WliResult<Option<String>> {
        #[cfg(windows)]
        {
            // On Windows, use SSPI for NTLM authentication
            self.create_windows_ntlm_header().await
        }
        #[cfg(not(windows))]
        {
            // On non-Windows systems, implement basic NTLM or fall back
            warn!("NTLM proxy authentication not fully supported on this platform");
            self.create_basic_auth_header()
        }
    }

    #[cfg(windows)]
    async fn create_windows_negotiate_header(&mut self) -> WliResult<Option<String>> {
        // Implementation for Windows NEGOTIATE authentication using SSPI
        // This would require additional Windows-specific dependencies like winapi
        warn!("Windows NEGOTIATE proxy authentication not yet implemented");
        Ok(None)
    }

    #[cfg(windows)]
    async fn create_windows_ntlm_header(&mut self) -> WliResult<Option<String>> {
        // Implementation for Windows NTLM authentication using SSPI
        // This would require additional Windows-specific dependencies like winapi
        warn!("Windows NTLM proxy authentication not yet implemented");
        self.create_basic_auth_header()
    }

    /// Get proxy configuration
    pub fn config(&self) -> &ProxyConfig {
        &self.config
    }

    /// Check if proxy requires authentication
    pub fn requires_auth(&self) -> bool {
        !matches!(self.config.auth_method, ProxyAuthMethod::None)
    }

    /// Get proxy URL
    pub fn url(&self) -> &str {
        &self.config.url
    }

    /// Get authentication method
    pub fn auth_method(&self) -> &ProxyAuthMethod {
        &self.config.auth_method
    }
}

/// Parse proxy authentication challenge from Proxy-Authenticate header
#[derive(Debug)]
pub struct ProxyAuthChallenge {
    pub schemes: Vec<ProxyAuthScheme>,
}

#[derive(Debug)]
pub struct ProxyAuthScheme {
    pub method: String,
    pub realm: Option<String>,
    pub parameters: HashMap<String, String>,
}

impl ProxyAuthChallenge {
    /// Parse Proxy-Authenticate header value
    pub fn parse(header_value: &str) -> WliResult<Self> {
        let mut schemes = Vec::new();
        
        // Split by comma, but be careful about quoted strings
        let parts = Self::split_auth_header(header_value);
        
        for part in parts {
            let part = part.trim();
            if part.is_empty() {
                continue;
            }

            let scheme = Self::parse_auth_scheme(part)?;
            schemes.push(scheme);
        }

        if schemes.is_empty() {
            return Err(WliError::proxy_auth("No valid authentication schemes found"));
        }

        Ok(Self { schemes })
    }

    /// Split authentication header while respecting quoted strings
    fn split_auth_header(header: &str) -> Vec<&str> {
        // Simple implementation - could be improved to handle complex cases
        header.split(',').collect()
    }

    /// Parse a single authentication scheme
    fn parse_auth_scheme(scheme_str: &str) -> WliResult<ProxyAuthScheme> {
        let parts: Vec<&str> = scheme_str.splitn(2, ' ').collect();
        if parts.is_empty() {
            return Err(WliError::proxy_auth("Invalid authentication scheme"));
        }

        let method = parts[0].to_string();
        let mut realm = None;
        let mut parameters = HashMap::new();

        if parts.len() > 1 {
            // Parse parameters
            for param in parts[1].split(',') {
                let param = param.trim();
                if let Some(eq_pos) = param.find('=') {
                    let key = param[..eq_pos].trim().to_string();
                    let value = param[eq_pos + 1..].trim().trim_matches('"').to_string();
                    
                    if key.to_lowercase() == "realm" {
                        realm = Some(value.clone());
                    }
                    parameters.insert(key, value);
                }
            }
        }

        Ok(ProxyAuthScheme {
            method,
            realm,
            parameters,
        })
    }

    /// Check if challenge supports a specific authentication method
    pub fn supports_method(&self, method: &ProxyAuthMethod) -> bool {
        let method_str = match method {
            ProxyAuthMethod::Basic => "basic",
            ProxyAuthMethod::Negotiate => "negotiate",
            ProxyAuthMethod::Ntlm => "ntlm",
            ProxyAuthMethod::None => return true,
        };

        self.schemes.iter().any(|scheme| {
            scheme.method.to_lowercase() == method_str
        })
    }

    /// Get the preferred authentication scheme
    pub fn preferred_scheme(&self) -> Option<&ProxyAuthScheme> {
        // Prefer in order: NEGOTIATE, NTLM, Basic
        for preferred in &["negotiate", "ntlm", "basic"] {
            if let Some(scheme) = self.schemes.iter().find(|s| s.method.to_lowercase() == *preferred) {
                return Some(scheme);
            }
        }
        self.schemes.first()
    }
}
