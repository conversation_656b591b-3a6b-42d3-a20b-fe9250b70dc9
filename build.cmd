@echo off
setlocal enabledelayedexpansion

:: Parse command line arguments
set "increment_version="
set "build_type=release"
set "show_help="

:parse_args
if "%~1"=="" goto :args_done
if /i "%~1"=="--increment" set "increment_version=true"
if /i "%~1"=="--minor" set "increment_version=true"
if /i "%~1"=="--debug" set "build_type=debug"
if /i "%~1"=="--help" set "show_help=true"
if /i "%~1"=="-h" set "show_help=true"
shift
goto :parse_args

:args_done

:: Show help if requested
if defined show_help (
    echo WebViewer CLI Build Script
    echo.
    echo Usage: build.cmd [OPTIONS]
    echo.
    echo Options:
    echo   --increment, --minor    Automatically increment minor version
    echo   --debug                 Build in debug mode instead of release
    echo   --help, -h             Show this help message
    echo.
    echo Examples:
    echo   build.cmd                    Build with current version
    echo   build.cmd --increment        Build and increment minor version
    echo   build.cmd --debug            Build debug version
    exit /b 0
)

:: Read current version from Cargo.toml
for /f "tokens=1,2 delims== " %%i in ('findstr /r /c:"^version *= *\"[0-9]*\.[0-9]*\.[0-9]*\"" Cargo.toml') do (
    set version=%%j
    set version=!version:"=!
)

:: Check if version is set
if not defined version (
    echo Error: Version not found in Cargo.toml
    exit /b 1
)

:: Increment version if requested
if defined increment_version (
    echo Current version: !version!
    call :increment_minor_version !version!
    if errorlevel 1 (
        echo Error: Failed to increment version
        exit /b 1
    )
    echo New version: !new_version!

    :: Update Cargo.toml with new version
    powershell -Command "(Get-Content 'Cargo.toml') -replace '^version = \"[0-9]+\.[0-9]+\.[0-9]+\"', 'version = \"!new_version!\"' | Set-Content 'Cargo.toml'"
    if errorlevel 1 (
        echo Error: Failed to update Cargo.toml
        exit /b 1
    )

    set version=!new_version!
    echo Version updated to: !version!
)

echo Build Version: !version!

:: Build the project
echo Building in %build_type% mode...
if "%build_type%"=="debug" (
    cargo build
    set "target_dir=target\debug"
) else (
    cargo build --release
    set "target_dir=target\release"
)

:: Check if build was successful
if errorlevel 1 (
    echo Error: Build failed
    exit /b 1
)

:: Create output filename with version and build type
if "%build_type%"=="debug" (
    set "output_name=wli-!version!-debug.exe"
) else (
    set "output_name=wli-!version!.exe"
)

:: Copy the binary with versioned name (don't rename to preserve original)
copy "!target_dir!\wli.exe" "!target_dir!\!output_name!" >nul

:: Check if copy was successful
if errorlevel 1 (
    echo Error: Failed to create versioned binary
    exit /b 1
)

echo.
echo ========================================
echo Build successful!
echo ========================================
echo Version: !version!
echo Build Type: %build_type%
echo Output: !target_dir!\!output_name!
echo Original: !target_dir!\wli.exe
echo ========================================

:: Show file sizes
if exist "!target_dir!\!output_name!" (
    for %%F in ("!target_dir!\!output_name!") do (
        echo File Size: %%~zF bytes
    )
)

goto :eof

:: Function to increment minor version (major.minor.patch)
:increment_minor_version
set "input_version=%~1"

:: Parse version components
for /f "tokens=1,2,3 delims=." %%a in ("%input_version%") do (
    set "major=%%a"
    set "minor=%%b"
    set "patch=%%c"
)

:: Increment minor version and reset patch to 0
set /a "minor=minor+1"
set "patch=0"

:: Construct new version
set "new_version=%major%.%minor%.%patch%"

goto :eof
