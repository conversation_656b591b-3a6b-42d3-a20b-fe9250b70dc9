@echo off
setlocal enabledelayedexpansion

:: Read Cargo.toml and extract the version
for /f "tokens=1,2 delims== " %%i in ('findstr /r /c:"^version *= *\"[0-9]*\.[0-9]*\.[0-9]*\"" Cargo.toml') do (
    set version=%%j
    set version=!version:"=!
)

:: Check if version is set
if not defined version (
    echo Version not found in Cargo.toml
    exit /b 1
)

echo Build Version: %version%

:: Build the project
cargo build --release

:: Check if build was successful
if errorlevel 1 (
    echo Build failed
    exit /b 1
)

:: Rename the output binary with the version
set output_name=wli-%version%.exe
ren target\release\wli.exe %output_name%

:: Check if rename was successful
if errorlevel 1 (
    echo Rename failed
    exit /b 1
)

echo Build successful: %output_name%
