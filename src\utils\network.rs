//! Network utilities and helpers.

use url::Url;

/// Remove port from localhost URLs for cleaner display
pub fn remove_port_if_localhost(url_str: &str) -> String {
    match Url::parse(url_str) {
        Ok(url) => {
            if url.host_str() == Some("localhost") || url.host_str() == Some("127.0.0.1") {
                format!("{}://{}{}", url.scheme(), url.host_str().unwrap(), url.path())
            } else {
                url.to_string()
            }
        }
        Err(_) => url_str.to_string(),
    }
    .trim_end_matches('/')
    .to_string()
}

/// Validate URL format
pub fn is_valid_url(url_str: &str) -> bool {
    Url::parse(url_str).is_ok()
}

/// Extract hostname from URL
pub fn extract_hostname(url_str: &str) -> Option<String> {
    Url::parse(url_str)
        .ok()
        .and_then(|url| url.host_str().map(String::from))
}

/// Check if URL uses HTTPS
pub fn is_https_url(url_str: &str) -> bool {
    Url::parse(url_str)
        .map(|url| url.scheme() == "https")
        .unwrap_or(false)
}
