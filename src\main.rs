use anyhow::Result;
use clap::CommandFactory;
use clap::Parser;
use log::info;
use log::LevelFilter;
use wli::{api::UploadAPI, Args};

#[tokio::main]
async fn main() -> Result<()> {
    // Parse arguments
    let args = Args::parse();

    // Set up logging
    env_logger::Builder::new()
        .filter_level(if args.verbose {
            LevelFilter::Debug
        } else {
            LevelFilter::Info
        })
        .init();

    // Handle --extensions flag
    if args.extensions {
        println!("{}", wli::utils::retrieve_extensions());
        return Ok(());
    }

    // Handle --help flag
    if args.help {
        Args::command().print_help()?;
        return Ok(());
    }

    // Check server health
    let mut upload_api = UploadAPI::new(&args);
    let _ = upload_api.check_health().await;

    // Handle --health flag
    if args.health {
        info!("Health check completed successfully");
        upload_api.do_logout().await?;
        std::process::exit(0);
    }

    // Run upload API
    upload_api.run().await?;

    info!("Upload operation completed successfully");
    Ok(())
}
