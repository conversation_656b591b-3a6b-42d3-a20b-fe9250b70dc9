use anyhow::Result;
use clap::CommandFactory;
use clap::Parser;
use log::info;
use log::LevelFilter;
use wli::{api::ApiClient, core::AppConfig, Args};

#[tokio::main]
async fn main() -> Result<()> {
    // Parse arguments
    let args = Args::parse();

    // Set up logging
    env_logger::Builder::new()
        .filter_level(if args.verbose {
            LevelFilter::Debug
        } else {
            LevelFilter::Info
        })
        .init();

    // Handle --extensions flag
    if args.extensions {
        println!("{}", wli::utils::retrieve_extensions());
        return Ok(());
    }

    // Handle --help flag
    if args.help {
        Args::command().print_help()?;
        return Ok(());
    }

    // Handle --license flag
    if args.license {
        match wli::utils::LicenseChecker::new() {
            Ok(checker) => {
                match checker.check_license_status(args.license_server.as_deref()) {
                    Ok(info) => {
                        println!("{}", checker.format_license_summary(&info));
                        return Ok(());
                    }
                    Err(e) => {
                        eprintln!("Error checking license status: {}", e);
                        std::process::exit(1);
                    }
                }
            }
            Err(e) => {
                eprintln!("Error initializing license checker: {}", e);
                std::process::exit(1);
            }
        }
    }

    // Create configuration from args
    let config = create_config_from_args(&args)?;
    let api_client = ApiClient::new(config)?;

    // Handle --health flag
    if args.health {
        match api_client.health_check().await {
            Ok(health_status) => {
                info!("Health check completed successfully");
                info!("Server status: {:?}", health_status);
                std::process::exit(0);
            }
            Err(e) => {
                eprintln!("Health check failed: {}", e);
                std::process::exit(1);
            }
        }
    }

    // Authenticate (credentials are already in the config)
    match api_client.authenticate().await {
        Ok(_) => info!("Authentication successful"),
        Err(e) => {
            eprintln!("Authentication failed: {}", e);
            std::process::exit(1);
        }
    }

    // Handle file uploads
    if let Some(files) = &args.files {
        let file_paths: Vec<String> = files.iter()
            .map(|f| f.as_path().to_string_lossy().into_owned())
            .collect();

        match api_client.upload_files(&file_paths, &args.target, args.use_id, args.permission_level).await {
            Ok(_) => info!("File upload completed successfully"),
            Err(e) => {
                eprintln!("File upload failed: {}", e);
                std::process::exit(1);
            }
        }
    }

    // Handle directory uploads
    if let Some(dir) = &args.dir {
        let dir_path = dir.as_path().to_string_lossy().into_owned();
        match api_client.upload_directory(&dir_path, &args.target, args.use_id, args.permission_level).await {
            Ok(_) => info!("Directory upload completed successfully"),
            Err(e) => {
                eprintln!("Directory upload failed: {}", e);
                std::process::exit(1);
            }
        }
    }

    // Handle folder creation
    if let Some(folder_name) = &args.name {
        match api_client.create_folder(folder_name, &args.target, args.use_id, args.permission_level).await {
            Ok(folder_id) => info!("Folder '{}' created successfully with ID: {}", folder_name, folder_id),
            Err(e) => {
                eprintln!("Folder creation failed: {}", e);
                std::process::exit(1);
            }
        }
    }

    info!("Operation completed successfully");
    Ok(())
}

/// Convert command line arguments to application configuration
fn create_config_from_args(args: &Args) -> Result<AppConfig> {
    use wli::core::{ServerConfig, AuthConfig, AuthMethod, UploadConfig, LoggingConfig, LogFormat};

    Ok(AppConfig {
        server: ServerConfig {
            host: args.host.clone(),
            timeout: args.timeout,
            insecure: args.insecure,
            max_retries: 3,
            retry_delay_ms: 1000,
        },
        auth: AuthConfig {
            username: args.usr.clone(),
            password: args.pwd.clone(),
            method: AuthMethod::Basic,
            token_cache_duration: 3600, // 1 hour default
        },
        upload: UploadConfig {
            chunk_size: 8192,
            max_concurrent: 4,
            default_permission_level: args.permission_level,
            supported_extensions: vec![], // Will use defaults
            max_file_size: 0, // Unlimited
        },
        proxy: None, // No proxy by default
        logging: LoggingConfig {
            level: if args.verbose { "debug".to_string() } else { "info".to_string() },
            colored: true,
            format: LogFormat::Pretty,
            file: None,
        },
    })
}
