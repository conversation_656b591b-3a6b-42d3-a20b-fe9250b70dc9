//! Upload service for file and folder operations.

use crate::core::{Wli<PERSON><PERSON><PERSON>, Wli<PERSON><PERSON><PERSON>};
use crate::http::HttpClient;
use crate::services::AuthService;
use crate::utils::{validate_file_exists, validate_directory_exists, get_files_in_dir, is_supported_file};
use reqwest::multipart::{Form, Part};
use serde_json::{json, Value};
use std::path::Path;
use std::sync::Arc;
use tokio::fs::File;
use tokio::sync::RwLock;
use tokio_util::codec::{BytesCodec, FramedRead};
use tracing::{debug, info, instrument};

/// Upload service for managing file uploads and folder operations
#[derive(Debug, Clone)]
pub struct UploadService {
    client: Arc<RwLock<HttpClient>>,
    auth_service: Arc<AuthService>,
    base_url: String,
}

impl UploadService {
    /// Create a new upload service
    pub fn new(
        client: HttpClient,
        auth_service: Arc<AuthService>,
        base_url: String,
    ) -> Self {
        Self {
            client: Arc::new(RwLock::new(client)),
            auth_service,
            base_url: base_url.trim_end_matches('/').to_string(),
        }
    }

    /// Upload files to the server
    #[instrument(skip(self, file_paths), fields(count = file_paths.len()))]
    pub async fn upload_files(
        &self,
        file_paths: &[String],
        target: &str,
        use_id: bool,
        permission_level: i8,
    ) -> WliResult<Value> {
        // Ensure authentication
        self.auth_service.refresh_token_if_needed().await?;

        // Validate files
        for file_path in file_paths {
            let path = Path::new(file_path);
            validate_file_exists(path)?;
            
            if !is_supported_file(path) {
                return Err(WliError::file_validation(format!(
                    "File extension not supported: {}", 
                    path.display()
                )));
            }
        }

        match file_paths.len() {
            0 => Err(WliError::invalid_argument("No files provided for upload".to_string())),
            1 => self.upload_single_file(&file_paths[0], target, use_id).await,
            _ => self.upload_bulk_files(file_paths, target, use_id).await,
        }
    }

    /// Upload files from a directory
    #[instrument(skip(self), fields(dir_path = %dir_path))]
    pub async fn upload_directory(
        &self,
        dir_path: &str,
        target: &str,
        use_id: bool,
        permission_level: i8,
    ) -> WliResult<Value> {
        let path = Path::new(dir_path);
        validate_directory_exists(path)?;

        let file_paths = get_files_in_dir(dir_path).map_err(|e| {
            WliError::file_system(format!("Failed to get files from directory: {}", e))
        })?;

        if file_paths.is_empty() {
            return Err(WliError::file_validation(
                "No supported files found in directory".to_string()
            ));
        }

        info!("Found {} files in directory {}", file_paths.len(), dir_path);
        self.upload_files(&file_paths, target, use_id, permission_level).await
    }

    /// Create a folder structure
    #[instrument(skip(self), fields(folder_name = %folder_name))]
    pub async fn create_folder(
        &self,
        folder_name: &str,
        parent_target: &str,
        use_id: bool,
        permission_level: i8,
    ) -> WliResult<String> {
        // Ensure authentication
        self.auth_service.refresh_token_if_needed().await?;

        let target_type = if use_id { "uploadId" } else { "uploadPath" };
        
        let create_url = format!("{}/api/folders", self.base_url);
        let folder_data = json!({
            "name": folder_name,
            target_type: parent_target,
            "permission": permission_level
        });

        let client = self.client.read().await;
        let response = client.post_json(&create_url, &folder_data).await?;
        drop(client);

        let response_data: Value = response.json().await.map_err(|e| {
            WliError::api(format!("Failed to parse folder creation response: {}", e))
        })?;

        let code = response_data.get("code")
            .and_then(|c| c.as_i64())
            .ok_or_else(|| WliError::api("Invalid response format"))?;

        if code != 200 {
            let message = response_data.get("message")
                .and_then(|m| m.as_str())
                .unwrap_or("Failed to create folder");
            return Err(WliError::api(message));
        }

        let folder_id = response_data.get("data")
            .and_then(|d| d.get("id"))
            .and_then(|id| id.as_str())
            .ok_or_else(|| WliError::api("Missing folder ID in response"))?;

        info!("Created folder '{}' with ID: {}", folder_name, folder_id);
        Ok(folder_id.to_string())
    }

    /// Upload a single file
    async fn upload_single_file(
        &self,
        file_path: &str,
        target: &str,
        use_id: bool,
    ) -> WliResult<Value> {
        let file = File::open(file_path).await.map_err(|e| {
            WliError::file_system(format!("Failed to open file {}: {}", file_path, e))
        })?;

        let stream = FramedRead::new(file, BytesCodec::new());
        let file_name = Path::new(file_path)
            .file_name()
            .and_then(|name| name.to_str())
            .ok_or_else(|| WliError::file_validation("Invalid file name".to_string()))?;

        let part = Part::stream(reqwest::Body::wrap_stream(stream))
            .file_name(file_name.to_string())
            .mime_str("application/octet-stream")
            .map_err(|e| WliError::api(format!("Failed to create file part: {}", e)))?;

        let target_type = if use_id { "uploadId" } else { "uploadPath" };
        let form = Form::new()
            .text(target_type, target.to_string())
            .part("fileUpload", part);

        let upload_url = format!("{}/api/files", self.base_url);
        
        let client = self.client.read().await;
        let response = client.post_multipart(&upload_url, form).await?;
        drop(client);

        let response_data: Value = response.json().await.map_err(|e| {
            WliError::api(format!("Failed to parse upload response: {}", e))
        })?;

        info!("Successfully uploaded file: {}", file_name);
        debug!("Upload response: {:?}", response_data);
        Ok(response_data)
    }

    /// Upload multiple files in bulk
    async fn upload_bulk_files(
        &self,
        file_paths: &[String],
        target: &str,
        use_id: bool,
    ) -> WliResult<Value> {
        let target_type = if use_id { "uploadId" } else { "uploadPath" };
        let mut form = Form::new().text(target_type, target.to_string());

        for file_path in file_paths {
            let file = File::open(file_path).await.map_err(|e| {
                WliError::file_system(format!("Failed to open file {}: {}", file_path, e))
            })?;

            let stream = FramedRead::new(file, BytesCodec::new());
            let file_name = Path::new(file_path)
                .file_name()
                .and_then(|name| name.to_str())
                .ok_or_else(|| WliError::file_validation("Invalid file name".to_string()))?;

            let part = Part::stream(reqwest::Body::wrap_stream(stream))
                .file_name(file_name.to_string())
                .mime_str("application/octet-stream")
                .map_err(|e| WliError::api(format!("Failed to create file part: {}", e)))?;

            form = form.part("fileUpload", part);
        }

        let upload_url = format!("{}/api/files/bulk", self.base_url);
        
        let client = self.client.read().await;
        let response = client.post_multipart(&upload_url, form).await?;
        drop(client);

        let response_data: Value = response.json().await.map_err(|e| {
            WliError::api(format!("Failed to parse bulk upload response: {}", e))
        })?;

        info!("Successfully uploaded {} files in bulk", file_paths.len());
        debug!("Bulk upload response: {:?}", response_data);
        Ok(response_data)
    }

    /// Get the base URL
    pub fn base_url(&self) -> &str {
        &self.base_url
    }
}
