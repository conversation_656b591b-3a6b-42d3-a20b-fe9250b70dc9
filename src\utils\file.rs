//! File handling utilities and validation.
//!
//! This module provides comprehensive file operations including validation,
//! directory traversal, and file type detection with support for various
//! engineering and CAD file formats.

use crate::core::{WliError, WliResult};
use std::fs;
use std::path::{Path, PathBuf};
use walkdir::WalkDir;
use globset::{Glob, GlobSetBuilder};
use tracing::debug;

/// Supported file extensions for upload
pub const SUPPORTED_EXTENSIONS: &[&str] = &[
    ".x_t", ".catpart", ".model", ".stp", ".sat", ".jt", ".prt", ".jth5", ".igs", ".sldptr",
    ".vtfx", ".bdf", ".op2", ".dwg", ".asm.9", ".tsg", ".wrl", ".ipt", ".zip", ".odb", ".adx",
];

/// Get file extension from a file path
pub fn get_extension(file_path: &Path) -> Option<String> {
    file_path.extension()
        .and_then(|ext| ext.to_str())
        .map(|ext| format!(".{}", ext.to_lowercase()))
}

/// Check if a file extension is supported
pub fn is_supported_extension(extension: &str) -> bool {
    let ext = if extension.starts_with('.') {
        extension.to_lowercase()
    } else {
        format!(".{}", extension.to_lowercase())
    };

    SUPPORTED_EXTENSIONS.contains(&ext.as_str())
}

/// Check if a file has a supported extension
pub fn is_supported_file(file_path: &Path) -> bool {
    get_extension(file_path)
        .map(|ext| is_supported_extension(&ext))
        .unwrap_or(false)
}

/// Validate that a file exists and is supported
pub fn validate_file(file_path: &Path) -> WliResult<()> {
    if !file_path.exists() {
        return Err(WliError::file_validation(format!(
            "File does not exist: {}",
            file_path.display()
        )));
    }

    if !file_path.is_file() {
        return Err(WliError::file_validation(format!(
            "Path is not a file: {}",
            file_path.display()
        )));
    }

    if !is_supported_file(file_path) {
        return Err(WliError::file_validation(format!(
            "File type not supported: {}",
            file_path.display()
        )));
    }

    Ok(())
}

/// Get all supported files in a directory
pub fn get_files_in_directory(dir_path: &Path) -> WliResult<Vec<PathBuf>> {
    if !dir_path.exists() {
        return Err(WliError::file_system(format!(
            "Directory does not exist: {}",
            dir_path.display()
        )));
    }

    if !dir_path.is_dir() {
        return Err(WliError::file_system(format!(
            "Path is not a directory: {}",
            dir_path.display()
        )));
    }

    let mut files = Vec::new();

    for entry in WalkDir::new(dir_path)
        .follow_links(false)
        .into_iter()
        .filter_map(|e| e.ok())
    {
        let path = entry.path();
        if path.is_file() && is_supported_file(path) {
            files.push(path.to_path_buf());
        }
    }

    debug!("Found {} supported files in directory: {}", files.len(), dir_path.display());
    Ok(files)
}

/// Get files matching a glob pattern
pub fn get_files_by_pattern(pattern: &str, base_dir: Option<&Path>) -> WliResult<Vec<PathBuf>> {
    let glob = Glob::new(pattern).map_err(|e| {
        WliError::file_validation(format!("Invalid glob pattern '{}': {}", pattern, e))
    })?;

    let mut builder = GlobSetBuilder::new();
    builder.add(glob);
    let glob_set = builder.build().map_err(|e| {
        WliError::file_validation(format!("Failed to build glob set: {}", e))
    })?;

    let search_dir = base_dir.unwrap_or_else(|| Path::new("."));
    let mut files = Vec::new();

    for entry in WalkDir::new(search_dir)
        .follow_links(false)
        .into_iter()
        .filter_map(|e| e.ok())
    {
        let path = entry.path();
        if path.is_file() {
            let relative_path = path.strip_prefix(search_dir).unwrap_or(path);
            if glob_set.is_match(relative_path) && is_supported_file(path) {
                files.push(path.to_path_buf());
            }
        }
    }

    debug!("Found {} files matching pattern '{}' in {}",
           files.len(), pattern, search_dir.display());
    Ok(files)
}

// TODO: Uncomment when UploadFile is available from core module
// /// Create UploadFile instances from file paths
// pub fn create_upload_files(file_paths: &[PathBuf]) -> WliResult<Vec<UploadFile>> {
//     let mut upload_files = Vec::new();
//
//     for path in file_paths {
//         match UploadFile::new(path.clone()) {
//             Ok(upload_file) => {
//                 if upload_file.is_supported() {
//                     upload_files.push(upload_file);
//                 } else {
//                     warn!("Skipping unsupported file: {}", path.display());
//                 }
//             }
//             Err(e) => {
//                 warn!("Failed to create upload file for {}: {}", path.display(), e);
//             }
//         }
//     }
//
//     debug!("Created {} upload files from {} paths", upload_files.len(), file_paths.len());
//     Ok(upload_files)
// }

/// Get file size in bytes
pub fn get_file_size(file_path: &Path) -> WliResult<u64> {
    let metadata = fs::metadata(file_path).map_err(|e| {
        WliError::file_system(format!("Failed to get file metadata for {}: {}", file_path.display(), e))
    })?;

    Ok(metadata.len())
}

/// Check if file size is within limits
pub fn validate_file_size(file_path: &Path, max_size: u64) -> WliResult<()> {
    if max_size == 0 {
        return Ok(());  // No size limit
    }

    let size = get_file_size(file_path)?;
    if size > max_size {
        return Err(WliError::file_validation(format!(
            "File {} is too large: {} bytes (max: {} bytes)",
            file_path.display(),
            size,
            max_size
        )));
    }

    Ok(())
}

/// Get a human-readable list of supported extensions
pub fn get_supported_extensions_list() -> String {
    SUPPORTED_EXTENSIONS.join(", ")
}

/// Check if a directory is empty
pub fn is_directory_empty(dir_path: &Path) -> WliResult<bool> {
    if !dir_path.is_dir() {
        return Err(WliError::file_system(format!(
            "Path is not a directory: {}",
            dir_path.display()
        )));
    }

    let mut entries = fs::read_dir(dir_path).map_err(|e| {
        WliError::file_system(format!("Failed to read directory {}: {}", dir_path.display(), e))
    })?;

    Ok(entries.next().is_none())
}

/// Create a directory if it doesn't exist
pub fn ensure_directory_exists(dir_path: &Path) -> WliResult<()> {
    if !dir_path.exists() {
        fs::create_dir_all(dir_path).map_err(|e| {
            WliError::file_system(format!("Failed to create directory {}: {}", dir_path.display(), e))
        })?;
        debug!("Created directory: {}", dir_path.display());
    }
    Ok(())
}

/// Get the MIME type for a file
pub fn get_mime_type(file_path: &Path) -> String {
    mime_guess::from_path(file_path)
        .first_or_octet_stream()
        .to_string()
}

/// Sanitize a filename for safe usage
pub fn sanitize_filename(filename: &str) -> String {
    filename
        .chars()
        .map(|c| match c {
            '<' | '>' | ':' | '"' | '|' | '?' | '*' => '_',
            c if c.is_control() => '_',
            c => c,
        })
        .collect()
}

/// Legacy compatibility function - get files in directory as strings
pub fn get_files_in_dir(dir: &str) -> anyhow::Result<Vec<String>> {
    let dir_path = Path::new(dir);
    let files = get_files_in_directory(dir_path)?;
    Ok(files.into_iter().map(|p| p.to_string_lossy().to_string()).collect())
}

/// Legacy compatibility function - get supported extensions list
pub fn retrieve_extensions() -> String {
    get_supported_extensions_list()
}
