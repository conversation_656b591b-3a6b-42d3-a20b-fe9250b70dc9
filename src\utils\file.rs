use anyhow::Result;
use std::fs;
use std::path::Path;

pub const ALLOWED_EXTENSIONS: &[&str] = &[
    ".x_t", ".catpart", ".model", ".stp", ".sat", ".jt", ".prt", ".jth5", ".igs", ".sldptr",
    ".vtfx", ".bdf", ".op2", ".dwg", ".asm.9", ".tsg", ".wrl", ".ipt", ".zip", ".odb", ".adx",
];

pub fn ext_of(file_path: &str) -> Option<&str> {
    Path::new(file_path).extension().and_then(|s| s.to_str())
}

pub fn is_valid_name(file_path: &str) -> bool {
    ext_of(file_path).map_or(false, |ext| {
        ALLOWED_EXTENSIONS.contains(&format!(".{}", ext).as_str())
    })
}

pub fn is_supported_file(entry: &fs::DirEntry) -> bool {
    entry.file_type().map_or(false, |ft| ft.is_file())
        && is_valid_name(entry.path().to_str().unwrap_or(""))
}

pub fn get_files_in_dir(dir: &str) -> Result<Vec<String>> {
    Ok(fs::read_dir(dir)?
        .filter_map(|entry| {
            entry.ok().and_then(|e| {
                if is_supported_file(&e) {
                    Some(e.path().to_string_lossy().into_owned())
                } else {
                    None
                }
            })
        })
        .collect())
}

pub fn retrieve_extensions() -> String {
    ALLOWED_EXTENSIONS.join(", ")
}
