//! Health check service for server status monitoring.

use crate::core::WliResult;
use crate::http::HttpClient;
use crate::services::AuthService;
use serde_json::Value;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, instrument, warn};

/// Health check service for monitoring server status
#[derive(Debug, Clone)]
pub struct HealthService {
    client: Arc<RwLock<HttpClient>>,
    auth_service: Option<Arc<AuthService>>,
    base_url: String,
}

impl HealthService {
    /// Create a new health service
    pub fn new(client: HttpClient, base_url: String) -> Self {
        Self {
            client: Arc::new(RwLock::new(client)),
            auth_service: None,
            base_url: base_url.trim_end_matches('/').to_string(),
        }
    }

    /// Create a new health service with authentication
    pub fn with_auth(
        client: HttpClient,
        auth_service: Arc<AuthService>,
        base_url: String,
    ) -> Self {
        Self {
            client: Arc::new(RwLock::new(client)),
            auth_service: Some(auth_service),
            base_url: base_url.trim_end_matches('/').to_string(),
        }
    }

    /// Check server health status
    #[instrument(skip(self), fields(base_url = %self.base_url))]
    pub async fn check_server_health(&self) -> WliResult<(Option<Value>, bool)> {
        let health_url = format!("{}/api/health", self.base_url);
        
        info!("Checking server health at: {}", health_url);
        
        let client = self.client.read().await;
        match client.get(&health_url).await {
            Ok(response) => {
                let status = response.status();
                debug!("Health check response status: {}", status);
                
                if status.is_success() {
                    match response.json::<Value>().await {
                        Ok(health_data) => {
                            info!("Server is healthy");
                            debug!("Health data: {:?}", health_data);
                            Ok((Some(health_data), true))
                        }
                        Err(e) => {
                            warn!("Failed to parse health response: {}", e);
                            // Server responded but with invalid JSON
                            Ok((None, true))
                        }
                    }
                } else {
                    warn!("Server health check failed with status: {}", status);
                    Ok((None, false))
                }
            }
            Err(e) => {
                warn!("Failed to connect to server: {}", e);
                Ok((None, false))
            }
        }
    }

    /// Check authentication status
    #[instrument(skip(self))]
    pub async fn check_authentication(&self) -> WliResult<bool> {
        if let Some(auth_service) = &self.auth_service {
            // Try to validate the current session
            match auth_service.validate_session().await {
                Ok(is_valid) => {
                    if is_valid {
                        info!("Authentication is valid");
                        Ok(true)
                    } else {
                        info!("Authentication is invalid, attempting re-authentication");
                        // Try to re-authenticate
                        match auth_service.authenticate().await {
                            Ok(_) => {
                                info!("Re-authentication successful");
                                Ok(true)
                            }
                            Err(e) => {
                                warn!("Re-authentication failed: {}", e);
                                Ok(false)
                            }
                        }
                    }
                }
                Err(e) => {
                    warn!("Session validation failed: {}", e);
                    Ok(false)
                }
            }
        } else {
            // No auth service configured, try a simple auth check
            self.check_auth_endpoint().await
        }
    }

    /// Check authentication endpoint directly
    async fn check_auth_endpoint(&self) -> WliResult<bool> {
        let auth_check_url = format!("{}/api/auth/check", self.base_url);
        
        let client = self.client.read().await;
        match client.get(&auth_check_url).await {
            Ok(response) => {
                let status = response.status();
                debug!("Auth check response status: {}", status);
                
                if status.is_success() {
                    match response.json::<Value>().await {
                        Ok(auth_data) => {
                            let is_authenticated = auth_data.get("authenticated")
                                .and_then(|a| a.as_bool())
                                .unwrap_or(false);
                            
                            if is_authenticated {
                                info!("Authentication check successful");
                            } else {
                                info!("Authentication check failed - not authenticated");
                            }
                            
                            Ok(is_authenticated)
                        }
                        Err(e) => {
                            warn!("Failed to parse auth check response: {}", e);
                            Ok(false)
                        }
                    }
                } else if status.as_u16() == 401 {
                    info!("Authentication check failed - unauthorized");
                    Ok(false)
                } else {
                    warn!("Auth check failed with status: {}", status);
                    Ok(false)
                }
            }
            Err(e) => {
                warn!("Failed to check authentication: {}", e);
                Ok(false)
            }
        }
    }

    /// Perform comprehensive health check including server and authentication
    #[instrument(skip(self))]
    pub async fn comprehensive_health_check(&self) -> WliResult<HealthStatus> {
        let (health_data, server_healthy) = self.check_server_health().await?;
        
        let auth_status = if server_healthy {
            self.check_authentication().await.unwrap_or(false)
        } else {
            false
        };

        Ok(HealthStatus {
            server_healthy,
            authenticated: auth_status,
            health_data,
        })
    }

    /// Get the base URL
    pub fn base_url(&self) -> &str {
        &self.base_url
    }
}

/// Health status information
#[derive(Debug, Clone)]
pub struct HealthStatus {
    /// Whether the server is responding and healthy
    pub server_healthy: bool,
    /// Whether authentication is working
    pub authenticated: bool,
    /// Raw health data from the server (if available)
    pub health_data: Option<Value>,
}

impl HealthStatus {
    /// Check if the system is fully operational
    pub fn is_operational(&self) -> bool {
        self.server_healthy && self.authenticated
    }

    /// Get a human-readable status message
    pub fn status_message(&self) -> String {
        match (self.server_healthy, self.authenticated) {
            (true, true) => "System is fully operational".to_string(),
            (true, false) => "Server is healthy but authentication failed".to_string(),
            (false, _) => "Server is not responding or unhealthy".to_string(),
        }
    }
}
