use std::time::Duration;

use serde::{Deserialize, Deserializer};

pub fn parse_duration<'de, D: Deserializer<'de>>(deserializer: D) -> Result<Duration, D::Error> {
    let err = |msg| <D::Error as serde::de::Error>::custom(msg);
    let s: String = Deserialize::deserialize(deserializer)?;
    let num_part = s.trim_end_matches(|c: char| !c.is_numeric());
    let suffix = &s[num_part.len()..];
    let num: u64 = num_part
        .parse()
        .map_err(|_| err("invalid number".to_string()))?;
    let ret = match suffix {
        "h" => Duration::from_secs(num * 3600),
        "m" => Duration::from_secs(num * 60),
        "s" => Duration::from_secs(num),
        "ms" => Duration::from_millis(num),
        other => return Err(err(format!("invalid suffix {other}"))),
    };
    Ok(ret)
}