//! Configuration management for the WLI CLI tool.
//! 
//! This module handles loading configuration from files, environment variables,
//! and command-line arguments with proper precedence and validation.

use crate::core::{Wli<PERSON><PERSON>r, WliResult};
use config::{Config, Environment, File};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// Main application configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AppConfig {
    /// Server configuration
    pub server: ServerConfig,
    /// Authentication configuration
    pub auth: AuthConfig,
    /// Upload configuration
    pub upload: UploadConfig,
    /// Proxy configuration
    pub proxy: Option<ProxyConfig>,
    /// Logging configuration
    pub logging: LoggingConfig,
}

/// Server connection configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    /// Server host URL
    pub host: Option<String>,
    /// Request timeout in seconds
    #[serde(default = "default_timeout")]
    pub timeout: u64,
    /// Disable SSL certificate verification
    #[serde(default)]
    pub insecure: bool,
    /// Maximum number of retry attempts
    #[serde(default = "default_max_retries")]
    pub max_retries: u32,
    /// Retry delay in milliseconds
    #[serde(default = "default_retry_delay")]
    pub retry_delay_ms: u64,
}

/// Authentication configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthConfig {
    /// Username
    pub username: Option<String>,
    /// Password
    pub password: Option<String>,
    /// Authentication method
    #[serde(default = "default_auth_method")]
    pub method: AuthMethod,
    /// Token cache duration in seconds
    #[serde(default = "default_token_cache_duration")]
    pub token_cache_duration: u64,
}

/// Authentication methods
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum AuthMethod {
    Basic,
    Bearer,
    Negotiate,
    Ntlm,
}

/// Upload configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UploadConfig {
    /// Chunk size for large file uploads (in bytes)
    #[serde(default = "default_chunk_size")]
    pub chunk_size: usize,
    /// Maximum concurrent uploads
    #[serde(default = "default_max_concurrent")]
    pub max_concurrent: usize,
    /// Default permission level for new folders
    #[serde(default = "default_permission_level")]
    pub default_permission_level: i8,
    /// Supported file extensions
    #[serde(default = "default_supported_extensions")]
    pub supported_extensions: Vec<String>,
    /// Maximum file size in bytes (0 = unlimited)
    #[serde(default)]
    pub max_file_size: u64,
}

/// Proxy configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyConfig {
    /// Proxy server URL
    pub url: String,
    /// Proxy authentication method
    #[serde(default = "default_proxy_auth")]
    pub auth_method: ProxyAuthMethod,
    /// Proxy username
    pub username: Option<String>,
    /// Proxy password
    pub password: Option<String>,
    /// Proxy domain (for NTLM)
    pub domain: Option<String>,
}

/// Proxy authentication methods
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "UPPERCASE")]
pub enum ProxyAuthMethod {
    Basic,
    Negotiate,
    Ntlm,
    None,
}

/// Logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    /// Log level
    #[serde(default = "default_log_level")]
    pub level: String,
    /// Enable colored output
    #[serde(default = "default_colored_output")]
    pub colored: bool,
    /// Log format
    #[serde(default = "default_log_format")]
    pub format: LogFormat,
    /// Log file path (optional)
    pub file: Option<PathBuf>,
}

/// Log format options
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum LogFormat {
    Compact,
    Pretty,
    Json,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            server: ServerConfig::default(),
            auth: AuthConfig::default(),
            upload: UploadConfig::default(),
            proxy: None,
            logging: LoggingConfig::default(),
        }
    }
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            host: None,
            timeout: default_timeout(),
            insecure: false,
            max_retries: default_max_retries(),
            retry_delay_ms: default_retry_delay(),
        }
    }
}

impl Default for AuthConfig {
    fn default() -> Self {
        Self {
            username: None,
            password: None,
            method: default_auth_method(),
            token_cache_duration: default_token_cache_duration(),
        }
    }
}

impl Default for UploadConfig {
    fn default() -> Self {
        Self {
            chunk_size: default_chunk_size(),
            max_concurrent: default_max_concurrent(),
            default_permission_level: default_permission_level(),
            supported_extensions: default_supported_extensions(),
            max_file_size: 0,
        }
    }
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: default_log_level(),
            colored: default_colored_output(),
            format: default_log_format(),
            file: None,
        }
    }
}

impl AppConfig {
    /// Load configuration from multiple sources with proper precedence
    pub fn load() -> WliResult<Self> {
        let mut config = Config::builder();

        // 1. Start with default configuration
        config = config.add_source(Config::try_from(&AppConfig::default())?);

        // 2. Load from config file if it exists
        if let Some(config_path) = Self::find_config_file() {
            config = config.add_source(File::from(config_path).required(false));
        }

        // 3. Load from environment variables
        config = config.add_source(
            Environment::with_prefix("WLI")
                .prefix_separator("_")
                .separator("__"),
        );

        let config = config.build().map_err(|e| {
            WliError::config(format!("Failed to load configuration: {}", e))
        })?;

        config.try_deserialize().map_err(|e| {
            WliError::config(format!("Failed to parse configuration: {}", e))
        })
    }

    /// Find configuration file in standard locations
    fn find_config_file() -> Option<PathBuf> {
        let config_names = ["wli.toml", "wli.yaml", "wli.yml", ".wli.toml"];
        
        // Check current directory first
        for name in &config_names {
            let path = PathBuf::from(name);
            if path.exists() {
                return Some(path);
            }
        }

        // Check user config directory
        if let Some(config_dir) = dirs::config_dir() {
            let app_config_dir = config_dir.join("wli");
            for name in &config_names {
                let path = app_config_dir.join(name);
                if path.exists() {
                    return Some(path);
                }
            }
        }

        None
    }

    /// Validate the configuration
    pub fn validate(&self) -> WliResult<()> {
        // Validate server configuration
        if self.server.timeout == 0 {
            return Err(WliError::config("Server timeout must be greater than 0"));
        }

        // Validate upload configuration
        if self.upload.max_concurrent == 0 {
            return Err(WliError::config("Max concurrent uploads must be greater than 0"));
        }

        if !(1..=3).contains(&self.upload.default_permission_level) {
            return Err(WliError::config("Default permission level must be between 1 and 3"));
        }

        // Validate proxy configuration
        if let Some(proxy) = &self.proxy {
            if proxy.url.is_empty() {
                return Err(WliError::config("Proxy URL cannot be empty"));
            }
        }

        Ok(())
    }
}

// Default value functions
fn default_timeout() -> u64 { 30 }
fn default_max_retries() -> u32 { 3 }
fn default_retry_delay() -> u64 { 1000 }
fn default_auth_method() -> AuthMethod { AuthMethod::Basic }
fn default_token_cache_duration() -> u64 { 3600 }
fn default_chunk_size() -> usize { 8 * 1024 * 1024 } // 8MB
fn default_max_concurrent() -> usize { 4 }
fn default_permission_level() -> i8 { 3 }
fn default_proxy_auth() -> ProxyAuthMethod { ProxyAuthMethod::None }
fn default_log_level() -> String { "info".to_string() }
fn default_colored_output() -> bool { true }
fn default_log_format() -> LogFormat { LogFormat::Compact }

fn default_supported_extensions() -> Vec<String> {
    vec![
        ".x_t", ".catpart", ".model", ".stp", ".sat", ".jt", ".prt", 
        ".jth5", ".igs", ".sldptr", ".vtfx", ".bdf", ".op2", ".dwg", 
        ".asm.9", ".tsg", ".wrl", ".ipt", ".zip", ".odb", ".adx"
    ].into_iter().map(String::from).collect()
}
