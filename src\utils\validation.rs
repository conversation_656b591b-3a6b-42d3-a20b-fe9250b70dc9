//! Validation utilities for files, paths, and user input.

use crate::core::{Wli<PERSON><PERSON>r, WliR<PERSON>ult};
use std::path::Path;

/// Validate that a file exists and is accessible
pub fn validate_file_exists(file_path: &Path) -> WliResult<()> {
    if !file_path.exists() {
        return Err(WliError::file_validation(format!(
            "File does not exist: {}",
            file_path.display()
        )));
    }

    if !file_path.is_file() {
        return Err(WliError::file_validation(format!(
            "Path is not a file: {}",
            file_path.display()
        )));
    }

    Ok(())
}

/// Validate that a file exists and has a supported extension (legacy compatibility)
pub fn validate_file_exists_legacy(file_path: &str) -> anyhow::Result<()> {
    let path = Path::new(file_path);
    if !path.is_file() {
        return Err(anyhow::anyhow!("File does not exist"));
    }

    if !crate::utils::is_supported_file(path) {
        return Err(anyhow::anyhow!("File extension is not supported"));
    }

    Ok(())
}

/// Validate that a directory exists and is accessible
pub fn validate_directory_exists(dir_path: &Path) -> WliResult<()> {
    if !dir_path.exists() {
        return Err(WliError::file_validation(format!(
            "Directory does not exist: {}",
            dir_path.display()
        )));
    }

    if !dir_path.is_dir() {
        return Err(WliError::file_validation(format!(
            "Path is not a directory: {}",
            dir_path.display()
        )));
    }

    Ok(())
}

/// Validate that a folder exists (legacy compatibility)
pub fn validate_folder_exists(folder_path: &str) -> anyhow::Result<()> {
    if !Path::new(folder_path).is_dir() {
        return Err(anyhow::anyhow!("Folder does not exist"));
    }
    Ok(())
}

/// Validate URL format
pub fn validate_url(url: &str) -> WliResult<()> {
    url::Url::parse(url).map_err(|e| {
        WliError::invalid_argument(format!("Invalid URL '{}': {}", url, e))
    })?;
    Ok(())
}

/// Validate that a string is not empty
pub fn validate_not_empty(value: &str, field_name: &str) -> WliResult<()> {
    if value.trim().is_empty() {
        return Err(WliError::invalid_argument(format!(
            "{} cannot be empty", 
            field_name
        )));
    }
    Ok(())
}

/// Validate permission level (1-3)
pub fn validate_permission_level(level: i8) -> WliResult<()> {
    if !(1..=3).contains(&level) {
        return Err(WliError::invalid_argument(format!(
            "Permission level must be between 1 and 3, got: {}", 
            level
        )));
    }
    Ok(())
}

/// Validate timeout value
pub fn validate_timeout(timeout: u64) -> WliResult<()> {
    if timeout == 0 {
        return Err(WliError::invalid_argument(
            "Timeout must be greater than 0".to_string()
        ));
    }
    if timeout > 3600 {
        return Err(WliError::invalid_argument(
            "Timeout cannot exceed 3600 seconds (1 hour)".to_string()
        ));
    }
    Ok(())
}

/// Validate that a path is safe (no directory traversal)
pub fn validate_safe_path(path: &str) -> WliResult<()> {
    if path.contains("..") {
        return Err(WliError::invalid_argument(
            "Path contains directory traversal sequences".to_string()
        ));
    }
    
    if path.starts_with('/') || path.contains(':') {
        return Err(WliError::invalid_argument(
            "Path appears to be absolute, only relative paths are allowed".to_string()
        ));
    }
    
    Ok(())
}
