//! Tests for proxy authentication scenarios, specifically 407 Proxy Authentication Required errors.

use mockito::{<PERSON><PERSON>, Server};
use std::time::Duration;
use tokio_test;
use wli::core::{AppConfig, AuthConfig, ProxyAuthMethod, ProxyConfig, ServerConfig, WliError};
use wli::http::HttpClient;

/// Helper function to create a test configuration with proxy settings
fn create_test_config_with_proxy(proxy_url: String, auth_method: ProxyAuthMethod) -> AppConfig {
    AppConfig {
        server: ServerConfig {
            base_url: "https://test-server.com".to_string(),
            timeout: 30,
            insecure: false,
            max_retries: 3,
            retry_delay_ms: 1000,
        },
        auth: AuthConfig {
            username: "testuser".to_string(),
            password: "testpass".to_string(),
            token: None,
        },
        proxy: Some(ProxyConfig {
            url: proxy_url,
            auth_method,
            username: Some("proxy_user".to_string()),
            password: Some("proxy_pass".to_string()),
            realm: Some("ADauth".to_string()),
        }),
        upload: Default::default(),
        logging: Default::default(),
    }
}

/// Helper function to create a test configuration without proxy
fn create_test_config_no_proxy() -> AppConfig {
    AppConfig {
        server: ServerConfig {
            base_url: "https://test-server.com".to_string(),
            timeout: 30,
            insecure: false,
            max_retries: 3,
            retry_delay_ms: 1000,
        },
        auth: AuthConfig {
            username: "testuser".to_string(),
            password: "testpass".to_string(),
            token: None,
        },
        proxy: None,
        upload: Default::default(),
        logging: Default::default(),
    }
}

#[tokio::test]
async fn test_407_proxy_authentication_required_basic() {
    // Create a mock server to simulate proxy behavior
    let mut server = Server::new_async().await;
    
    // Mock the proxy returning 407 Proxy Authentication Required
    let mock = server
        .mock("GET", "/api/health")
        .match_header("proxy-authorization", Matcher::Missing)
        .with_status(407)
        .with_header("proxy-authenticate", "Basic realm=\"ADauth\"")
        .with_body("Proxy Authentication Required")
        .create_async()
        .await;

    // Create configuration with Basic proxy auth
    let config = create_test_config_with_proxy(
        server.url(),
        ProxyAuthMethod::Basic,
    );

    // Create HTTP client
    let client = HttpClient::new(config).expect("Failed to create HTTP client");

    // Make a request that should trigger 407 error
    let result = client.get(&format!("{}/api/health", server.url())).await;

    // Verify that we get a 407 error
    assert!(result.is_err());
    if let Err(WliError::Server { code, message }) = result {
        assert_eq!(code, 407);
        assert!(message.contains("407") || message.contains("Proxy Authentication"));
    } else {
        panic!("Expected Server error with code 407, got: {:?}", result);
    }

    mock.assert_async().await;
}

#[tokio::test]
async fn test_407_proxy_authentication_required_ntlm() {
    let mut server = Server::new_async().await;
    
    // Mock the proxy returning 407 with NTLM challenge
    let mock = server
        .mock("GET", "/api/health")
        .with_status(407)
        .with_header("proxy-authenticate", "NTLM")
        .with_body("Proxy Authentication Required - NTLM")
        .create_async()
        .await;

    let config = create_test_config_with_proxy(
        server.url(),
        ProxyAuthMethod::Ntlm,
    );

    let client = HttpClient::new(config).expect("Failed to create HTTP client");
    let result = client.get(&format!("{}/api/health", server.url())).await;

    assert!(result.is_err());
    if let Err(WliError::Server { code, .. }) = result {
        assert_eq!(code, 407);
    } else {
        panic!("Expected Server error with code 407, got: {:?}", result);
    }

    mock.assert_async().await;
}

#[tokio::test]
async fn test_407_proxy_authentication_required_negotiate() {
    let mut server = Server::new_async().await;
    
    // Mock the proxy returning 407 with Negotiate challenge
    let mock = server
        .mock("GET", "/api/health")
        .with_status(407)
        .with_header("proxy-authenticate", "Negotiate")
        .with_body("Proxy Authentication Required - Negotiate")
        .create_async()
        .await;

    let config = create_test_config_with_proxy(
        server.url(),
        ProxyAuthMethod::Negotiate,
    );

    let client = HttpClient::new(config).expect("Failed to create HTTP client");
    let result = client.get(&format!("{}/api/health", server.url())).await;

    assert!(result.is_err());
    if let Err(WliError::Server { code, .. }) = result {
        assert_eq!(code, 407);
    } else {
        panic!("Expected Server error with code 407, got: {:?}", result);
    }

    mock.assert_async().await;
}

#[tokio::test]
async fn test_407_error_no_retry_behavior() {
    let mut server = Server::new_async().await;
    
    // Mock that should only be called once (no retries for 407)
    let mock = server
        .mock("GET", "/api/health")
        .with_status(407)
        .with_header("proxy-authenticate", "Basic realm=\"ADauth\"")
        .with_body("Proxy Authentication Required")
        .expect(1) // Should only be called once, no retries
        .create_async()
        .await;

    let config = create_test_config_with_proxy(
        server.url(),
        ProxyAuthMethod::Basic,
    );

    let client = HttpClient::new(config).expect("Failed to create HTTP client");
    
    // Record start time to verify no retry delay
    let start_time = std::time::Instant::now();
    let result = client.get(&format!("{}/api/health", server.url())).await;
    let elapsed = start_time.elapsed();

    // Verify error and that it returned quickly (no retry delay)
    assert!(result.is_err());
    assert!(elapsed < Duration::from_millis(500)); // Should be much faster than retry delay

    if let Err(WliError::Server { code, .. }) = result {
        assert_eq!(code, 407);
    } else {
        panic!("Expected Server error with code 407, got: {:?}", result);
    }

    mock.assert_async().await;
}

#[tokio::test]
async fn test_407_with_multiple_auth_methods() {
    let mut server = Server::new_async().await;
    
    // Mock proxy offering multiple authentication methods
    let mock = server
        .mock("GET", "/api/health")
        .with_status(407)
        .with_header("proxy-authenticate", "Basic realm=\"ADauth\"")
        .with_header("proxy-authenticate", "NTLM")
        .with_header("proxy-authenticate", "Negotiate")
        .with_body("Multiple authentication methods available")
        .create_async()
        .await;

    let config = create_test_config_with_proxy(
        server.url(),
        ProxyAuthMethod::Basic,
    );

    let client = HttpClient::new(config).expect("Failed to create HTTP client");
    let result = client.get(&format!("{}/api/health", server.url())).await;

    assert!(result.is_err());
    if let Err(WliError::Server { code, message }) = result {
        assert_eq!(code, 407);
        // Verify the error message contains useful information
        assert!(message.contains("407") || message.contains("Client error"));
    } else {
        panic!("Expected Server error with code 407, got: {:?}", result);
    }

    mock.assert_async().await;
}

#[tokio::test]
async fn test_successful_request_after_proxy_auth() {
    let mut server = Server::new_async().await;
    
    // Mock successful response after proper proxy authentication
    let mock = server
        .mock("GET", "/api/health")
        .match_header("proxy-authorization", Matcher::Any)
        .with_status(200)
        .with_header("content-type", "application/json")
        .with_body(r#"{"status": "healthy", "authenticated": true}"#)
        .create_async()
        .await;

    let config = create_test_config_with_proxy(
        server.url(),
        ProxyAuthMethod::Basic,
    );

    let client = HttpClient::new(config).expect("Failed to create HTTP client");
    let result = client.get(&format!("{}/api/health", server.url())).await;

    // This test verifies that when proxy auth is properly configured,
    // requests should succeed (though actual proxy auth implementation
    // may vary based on the underlying HTTP client capabilities)
    match result {
        Ok(response) => {
            assert_eq!(response.status(), 200);
        }
        Err(WliError::Server { code, .. }) if code == 407 => {
            // This is also acceptable as it indicates the proxy auth challenge was received
            // The actual authentication negotiation depends on the HTTP client implementation
        }
        Err(e) => {
            panic!("Unexpected error: {:?}", e);
        }
    }

    mock.assert_async().await;
}

#[tokio::test]
async fn test_proxy_auth_error_message_clarity() {
    let mut server = Server::new_async().await;
    
    let mock = server
        .mock("GET", "/api/health")
        .with_status(407)
        .with_header("proxy-authenticate", "Basic realm=\"Corporate Proxy\"")
        .with_body("Access denied. Please authenticate with the corporate proxy.")
        .create_async()
        .await;

    let config = create_test_config_with_proxy(
        server.url(),
        ProxyAuthMethod::Basic,
    );

    let client = HttpClient::new(config).expect("Failed to create HTTP client");
    let result = client.get(&format!("{}/api/health", server.url())).await;

    assert!(result.is_err());
    if let Err(error) = result {
        let error_message = error.to_string();
        // Verify that the error message is informative for troubleshooting
        assert!(
            error_message.contains("407") || 
            error_message.contains("Proxy") || 
            error_message.contains("Client error"),
            "Error message should be informative: {}", error_message
        );
    }

    mock.assert_async().await;
}
